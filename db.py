from sqlmodel import SQLModel, Session, create_engine
from sqlalchemy.pool import <PERSON>ull<PERSON><PERSON>
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get DATABASE_URL from environment variables
database_url = os.getenv("DATABASE_URL")

if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

# Ensure correct PostgreSQL format
database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)

# Create the SQLAlchemy engine with better connection handling
engine = create_engine(
    database_url,
    echo=False,  # Set to True for debugging
    pool_pre_ping=True,  # Test connections before use
    pool_recycle=300,  # Recycle connections every 5 minutes
    pool_timeout=30,  # Connection timeout
    max_overflow=10,  # Allow some overflow connections
    pool_size=5,  # Maintain 5 connections in the pool
)

def create_db_and_tables():
    """Create database tables based on SQLModel definitions."""
    # Import all models to ensure they're registered with SQLModel metadata
    # This import is used to register the model with SQLModel metadata
    # pylint: disable=unused-import
    from routes.account import AccountCredential
    from models.user import User
    from models.user_verification import UserVerification
    from models.referral import ReferralPoints, WithdrawalRequest, GiveawayEntry
    from models.order import OrderModel, CompleteOrderModel, FailOrder, RejectOrder, PassOrder, OrderTimeline, LiveAccount, Stage2Account

    try:
        # Create all tables
        SQLModel.metadata.create_all(engine)
        print("✅ Database tables created successfully")

        # Explicitly create the account_credentials table
        try:
            AccountCredential.create_table(engine)
            print("✅ Ensured account_credentials table exists")
        except Exception as e:
            print(f"⚠️  Error ensuring account_credentials table: {str(e)}")

        # Add created_at column to ordermodel table if it doesn't exist
        try:
            with Session(engine) as session:
                # Check if the column exists
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name='ordermodel' AND column_name='created_at';
                """))

                if not result.fetchone():
                    print("Adding created_at column to ordermodel table...")
                    session.execute(text("""
                        ALTER TABLE ordermodel
                        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                    """))
                    session.commit()
                    print("✅ created_at column added successfully.")
                else:
                    print("✅ created_at column already exists in ordermodel table.")
        except Exception as e:
            print(f"⚠️  Error checking/adding created_at column: {str(e)}")

        # Add missing columns for referral system
        try:
            with Session(engine) as session:
                # Check and add referred_by column to user table
                result = session.execute(text("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name='user' AND column_name='referred_by';
                """))

                if not result.fetchone():
                    print("Adding referred_by column to user table...")
                    session.execute(text("""
                        ALTER TABLE "user" ADD COLUMN referred_by VARCHAR;
                    """))
                    session.commit()
                    print("✅ referred_by column added successfully.")
                else:
                    print("✅ referred_by column already exists in user table.")

                # Check and add completed_at column to completeordermodel table
                result = session.execute(text("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name='completeordermodel' AND column_name='completed_at';
                """))

                if not result.fetchone():
                    print("Adding completed_at column to completeordermodel table...")
                    session.execute(text("""
                        ALTER TABLE completeordermodel ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                    """))
                    session.commit()
                    print("✅ completed_at column added successfully.")
                else:
                    print("✅ completed_at column already exists in completeordermodel table.")

        except Exception as e:
            print(f"⚠️  Error checking/adding referral columns: {str(e)}")

    except Exception as e:
        print(f"❌ Error creating database tables: {str(e)}")
        raise

def get_session():
    """Provide a database session using a context manager."""
    with Session(engine) as session:
        yield session
