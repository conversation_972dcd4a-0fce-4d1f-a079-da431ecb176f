# reCAPTCHA v2 Integration Setup

This backend now uses **Google reCAPTCHA v2** for bot/spam protection on authentication endpoints (signup, login, forgot password).

## Backend Setup

1. **Environment Variables**
   - `RECAPTCHA_SECRET_KEY`: Your Google reCAPTCHA v2 secret key
   - `RECAPTCHA_SITE_KEY`: Your Google reCAPTCHA v2 site key

2. **Endpoints**
   - All major authentication endpoints require a valid reCAPTCHA v2 token from the frontend.
   - The backend verifies the token with Google and rejects requests with missing or invalid tokens.

## Frontend Setup

1. **Add reCAPTCHA v2 Widget**
   - Use the [Google reCAPTCHA v2 documentation](https://developers.google.com/recaptcha/docs/display) to add the widget to your login, signup, and forgot password forms.
   - Example (Checkbox):
     ```html
     <form id="login-form">
       <!-- your fields -->
       <div class="g-recaptcha" data-sitekey="YOUR_SITE_KEY"></div>
       <button type="submit">Login</button>
     </form>
     <script src="https://www.google.com/recaptcha/api.js" async defer></script>
     ```
   - On submit, get the value from the widget and send it as `recaptcha_token` to the backend.

2. **Send Token to Backend**
   - The token is available as the value of the reCAPTCHA widget (usually in a hidden input or via JS):
     ```js
     var token = grecaptcha.getResponse();
     // Send token to backend as recaptcha_token
     ```

## Testing
- Use real tokens from the widget for successful verification.
- Test with missing/invalid tokens to ensure backend rejects them.

## Notes
- The backend no longer checks a score (as in v3); it only checks the 'success' field from Google's response.
- You must use the v2 widget and keys from the [Google reCAPTCHA admin console](https://www.google.com/recaptcha/admin/create). 