# Admin Authentication System

This document describes the admin authentication system with 2FA support for the Funded Horizon trading platform.

## Overview

The admin system provides secure authentication for administrative users with the following features:

- **Separate Admin Authentication**: Completely independent from regular user authentication
- **Two-Factor Authentication (2FA)**: Optional TOTP-based 2FA using authenticator apps
- **Role-Based Access**: Support for different admin roles (admin, super_admin)
- **Secure JWT Tokens**: Admin-specific JWT tokens with type validation

## Database Models

### Admin Model
```python
class Admin(SQLModel, table=True):
    __tablename__ = "admins"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(unique=True, index=True)
    email: EmailStr = Field(unique=True, index=True)
    hashed_password: str
    name: str
    role: str = Field(default="admin")  # admin, super_admin
    is_active: bool = Field(default=True)
    two_factor_secret: Optional[str] = Field(default=None)
    two_factor_enabled: bool = Field(default=False)
    last_login: Optional[datetime] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
```

### Admin2FAToken Model
```python
class Admin2FAToken(SQLModel, table=True):
    __tablename__ = "admin_2fa_tokens"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    admin_id: int = Field(foreign_key="admins.id")
    token: str = Field(index=True, unique=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    is_used: bool = Field(default=False)
```

## API Endpoints

### 1. Admin Signup
**POST** `/admin/signup`

Creates a new admin account.

**Request Body:**
```json
{
    "username": "admin_user",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "name": "Admin User",
    "role": "admin"
}
```

**Response:**
```json
{
    "id": 1,
    "username": "admin_user",
    "email": "<EMAIL>",
    "name": "Admin User",
    "role": "admin",
    "is_active": true,
    "two_factor_enabled": false,
    "last_login": null,
    "created_at": "2025-01-20T10:30:00Z"
}
```

### 2. Admin Login
**POST** `/admin/login`

Authenticates an admin user. If 2FA is enabled, returns a 2FA token for verification.

**Request Body:**
```json
{
    "username": "admin_user",
    "password": "SecurePassword123!"
}
```

**Response (No 2FA):**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "admin": {
        "id": 1,
        "username": "admin_user",
        "email": "<EMAIL>",
        "name": "Admin User",
        "role": "admin",
        "is_active": true,
        "two_factor_enabled": false,
        "last_login": "2025-01-20T10:30:00Z",
        "created_at": "2025-01-20T10:30:00Z"
    }
}
```

**Response (With 2FA):**
```json
{
    "message": "2FA required",
    "two_factor_required": true,
    "two_factor_token": "abc123def456..."
}
```

### 3. 2FA Verification
**POST** `/admin/verify-2fa`

Verifies 2FA code and completes login process.

**Request Body:**
```json
{
    "token": "abc123def456...",
    "two_factor_code": "123456"
}
```

**Response:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "admin": {
        "id": 1,
        "username": "admin_user",
        "email": "<EMAIL>",
        "name": "Admin User",
        "role": "admin",
        "is_active": true,
        "two_factor_enabled": true,
        "last_login": "2025-01-20T10:30:00Z",
        "created_at": "2025-01-20T10:30:00Z"
    }
}
```

### 4. Setup 2FA
**POST** `/admin/setup-2fa`

Generates 2FA secret and QR code for setup.

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
    "secret_key": "JBSWY3DPEHPK3PXP",
    "qr_code_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
}
```

### 5. Enable 2FA
**POST** `/admin/enable-2fa`

Enables 2FA after setup and verification.

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
    "two_factor_code": "123456"
}
```

**Response:**
```json
{
    "message": "2FA enabled successfully"
}
```

### 6. Disable 2FA
**POST** `/admin/disable-2FA`

Disables 2FA for the admin account.

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
    "two_factor_code": "123456"
}
```

**Response:**
```json
{
    "message": "2FA disabled successfully"
}
```

### 7. Get Admin Profile
**GET** `/admin/me`

Retrieves the current admin's profile information.

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
    "id": 1,
    "username": "admin_user",
    "email": "<EMAIL>",
    "name": "Admin User",
    "role": "admin",
    "is_active": true,
    "two_factor_enabled": true,
    "last_login": "2025-01-20T10:30:00Z",
    "created_at": "2025-01-20T10:30:00Z"
}
```

## Authentication Flow

### Without 2FA
1. Admin submits username/password to `/admin/login`
2. System validates credentials
3. System returns JWT access token
4. Admin uses token for subsequent requests

### With 2FA
1. Admin submits username/password to `/admin/login`
2. System validates credentials
3. System returns 2FA token (not JWT)
4. Admin submits 2FA code to `/admin/verify-2fa`
5. System validates 2FA code
6. System returns JWT access token
7. Admin uses token for subsequent requests

## 2FA Setup Process

1. **Setup 2FA**: Call `/admin/setup-2fa` to get secret key and QR code
2. **Scan QR Code**: Use authenticator app (Google Authenticator, Authy, etc.) to scan QR code
3. **Verify Setup**: Call `/admin/enable-2fa` with a code from the authenticator app
4. **2FA Enabled**: Future logins will require 2FA verification

## Security Features

- **Separate Token Type**: Admin JWT tokens include `"type": "admin"` to distinguish from user tokens
- **Password Hashing**: Passwords are hashed using bcrypt
- **Token Expiration**: JWT tokens expire based on configuration
- **2FA Token Expiration**: 2FA verification tokens expire after 5 minutes
- **Account Status**: Inactive admin accounts cannot authenticate
- **Unique Constraints**: Username and email must be unique

## Dependencies

Add these to your `requirements.txt`:
```
pyotp>=2.9.0
qrcode[pil]>=7.4.2
Pillow>=10.0.0
```

## Testing

Run the test script to verify admin endpoints:
```bash
python test_admin_endpoints.py
```

## Usage Examples

### Creating an Admin Account
```python
import requests

# Create admin account
signup_data = {
    "username": "admin_user",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "name": "Admin User",
    "role": "admin"
}

response = requests.post("http://localhost:8000/admin/signup", json=signup_data)
print(response.json())
```

### Admin Login with 2FA
```python
# Step 1: Login with username/password
login_data = {
    "username": "admin_user",
    "password": "SecurePassword123!"
}

response = requests.post("http://localhost:8000/admin/login", json=login_data)
result = response.json()

if result.get("two_factor_required"):
    # Step 2: Verify 2FA code
    twofa_data = {
        "token": result["two_factor_token"],
        "two_factor_code": "123456"  # From authenticator app
    }
    
    verify_response = requests.post("http://localhost:8000/admin/verify-2fa", json=twofa_data)
    access_token = verify_response.json()["access_token"]
else:
    access_token = result["access_token"]

# Use access token for authenticated requests
headers = {"Authorization": f"Bearer {access_token}"}
profile_response = requests.get("http://localhost:8000/admin/me", headers=headers)
print(profile_response.json())
```

## Error Handling

Common error responses:

- **401 Unauthorized**: Invalid credentials or inactive account
- **400 Bad Request**: Invalid 2FA code or expired token
- **404 Not Found**: Admin not found
- **500 Internal Server Error**: Server-side error

## Notes

- Admin authentication is completely separate from user authentication
- Admin tokens cannot be used for user endpoints and vice versa
- 2FA is optional and can be enabled/disabled by the admin
- All admin endpoints are prefixed with `/admin`
- Admin tokens include a `type` field to prevent token confusion 