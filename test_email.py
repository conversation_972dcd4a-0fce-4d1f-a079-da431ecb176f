# -*- coding: utf-8 -*-
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultip<PERSON>

def test_email_configuration():
    """Test the email configuration to identify any issues"""
    
    # Email configuration
    from_email = "<EMAIL>"
    from_password = "Fundedwhales@123"
    to_email = "<EMAIL>"  # Replace with a real email for testing
    
    try:
        # Create message
        msg = MIMEMultipart()
        msg["From"] = from_email
        msg["To"] = to_email
        msg["Subject"] = "Test Email - Funded Whales"
        
        # Create HTML content
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Test Email</title>
        </head>
        <body style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%); color: #1565c0; font-family: Arial, sans-serif; min-height: 100vh; margin: 0; padding: 20px;">
            <div style="max-width: 600px; margin: 0 auto; padding: 30px; background: rgba(255, 255, 255, 0.9); border-radius: 10px; box-shadow: 0 4px 6px rgba(33, 150, 243, 0.3); border: 1px solid rgba(66, 165, 245, 0.3);">
                <h1 style="color: #1976d2; text-align: center; margin-bottom: 20px;">Funded Whales - Test Email</h1>
                <p style="color: #1565c0; font-size: 16px; line-height: 1.6;">This is a test email to verify the email configuration is working properly for Funded Whales.</p>
                <p style="color: #42a5f5; font-size: 16px; line-height: 1.6; background: rgba(227, 242, 253, 0.7); padding: 15px; border-radius: 5px; border-left: 4px solid #42a5f5;">If you receive this email, the email system is working correctly.</p>
            </div>
        </body>
        </html>
        """
        
        # Attach HTML content
        msg.attach(MIMEText(html_content, "html", "utf-8"))
        
        print("Testing SMTP connection...")
        
        # Test SMTP connection
        with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:
            print("SMTP connection established successfully")
            
            print("Attempting to login...")
            server.login(from_email, from_password)
            print("Login successful")
            
            print("Attempting to send email...")
            server.sendmail(from_email, to_email, msg.as_string())
            print("Email sent successfully!")
            
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"SMTP Authentication Error: {e}")
        print("This usually means the email or password is incorrect")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"SMTP Connection Error: {e}")
        print("This usually means the SMTP server is not accessible")
        return False
        
    except smtplib.SMTPException as e:
        print(f"SMTP Error: {e}")
        return False
        
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("Testing email configuration...")
    success = test_email_configuration()
    
    if success:
        print("\n✅ Email configuration is working correctly!")
    else:
        print("\n❌ Email configuration has issues. Please check the error messages above.") 