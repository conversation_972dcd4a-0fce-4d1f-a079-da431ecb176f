import os
import psycopg2
from psycopg2.extras import RealDictCursor
from config import DATABASE_URL

def check_user_table_schema():
    """Check the exact schema of the user table"""
    try:
        print(f"Connecting to database...")
    
        # Connect to database
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check if user table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'user'
            );
        """)
        table_exists = cursor.fetchone()['exists']
        
        if not table_exists:
            print("❌ User table does not exist!")
            return
        
        print("✅ User table exists")
        
        # Get all columns in the user table
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'user'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print(f"\n📋 User table columns ({len(columns)} total):")
        print("-" * 80)
        for col in columns:
            print(f"Column: {col['column_name']:<20} Type: {col['data_type']:<15} Nullable: {col['is_nullable']:<5} Default: {col['column_default']}")
        
        # Check for specific missing columns
        column_names = [col['column_name'] for col in columns]
        
        missing_columns = []
        expected_columns = [
            'id', 'username', 'email', 'hashed_password', 'name', 
            'phone_no', 'country', 'address', 'created_at', 
            'referral_code', 'referred_by', 'total_points'
        ]
        
        for expected_col in expected_columns:
            if expected_col not in column_names:
                missing_columns.append(expected_col)
        
        if missing_columns:
            print(f"\n❌ Missing columns: {missing_columns}")
            else:
            print(f"\n✅ All expected columns are present")
        
        # Check table structure
        cursor.execute("""
            SELECT 
                table_name,
                column_name,
                data_type,
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'user'
            ORDER BY ordinal_position;
        """)
        
        print(f"\n🔍 Detailed table structure:")
        print("-" * 80)
        for row in cursor.fetchall():
            print(f"{row['column_name']:<20} {row['data_type']:<15} {row['is_nullable']:<5} {row['column_default']}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking user table schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_user_table_schema()
