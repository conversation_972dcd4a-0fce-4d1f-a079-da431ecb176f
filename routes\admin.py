from fastapi import APIRouter, Depends, HTTPException, status, Form
from sqlmodel import Session, select
from models.admin import Admin, Admin2FAToken
from schemas.admin import AdminCreate, AdminLogin, Admin2FAVerify, AdminResponse, AdminToken, Admin2FASetup
from admin_auth import (
    get_admin_password_hash, 
    verify_admin_password, 
    create_admin_access_token, 
    get_current_admin,
    generate_2fa_secret,
    generate_2fa_qr_code,
    verify_2fa_code,
    create_2fa_token_record
)
from db import get_session
from datetime import datetime, timedelta

admin_router = APIRouter(prefix="/admin", tags=["Admin"])

# Admin Signup Endpoint
@admin_router.post("/signup", response_model=AdminResponse)
def admin_signup(admin_create: AdminCreate, session: Session = Depends(get_session)):
    try:
        # Check if admin already exists
        admin_exists = session.exec(
            select(Admin).where(
                (Admin.email == admin_create.email) | (Admin.username == admin_create.username)
            )
        ).first()
        
        if admin_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Admin with this email or username already exists"
            )

        # Hash the password and create admin
        hashed_password = get_admin_password_hash(admin_create.password)
        new_admin = Admin(
            username=admin_create.username,
            email=admin_create.email,
            hashed_password=hashed_password,
            name=admin_create.name,
            role=admin_create.role
        )
        
        session.add(new_admin)
        session.commit()
        session.refresh(new_admin)

        # Return admin response without sensitive data
        return AdminResponse(
            id=new_admin.id,
            username=new_admin.username,
            email=new_admin.email,
            name=new_admin.name,
            role=new_admin.role,
            is_active=new_admin.is_active,
            two_factor_enabled=new_admin.two_factor_enabled,
            last_login=new_admin.last_login,
            created_at=new_admin.created_at
        )

    except Exception as e:
        session.rollback()
        print(f"Error in admin signup: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during admin signup. Please try again."
        )

# Admin Login Endpoint (Step 1: Username/Password)
@admin_router.post("/login")
def admin_login(
    username: str = Form(...),
    password: str = Form(...),
    session: Session = Depends(get_session)
):
    try:
        # Find admin by username
        admin = session.exec(select(Admin).where(Admin.username == username)).first()
        if not admin:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, 
                detail="Invalid credentials"
            )

        # Verify password
        if not verify_admin_password(password, admin.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, 
                detail="Invalid credentials"
            )

        # Check if admin is active
        if not admin.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, 
                detail="Admin account is inactive"
            )

        # Check if 2FA is enabled
        if admin.two_factor_enabled:
            # Generate 2FA token for verification
            two_factor_token = create_2fa_token_record(admin.id, session)
            
            return {
                "message": "2FA required",
                "two_factor_required": True,
                "two_factor_token": two_factor_token
            }
        else:
            # No 2FA required, create access token directly
            access_token = create_admin_access_token(data={"sub": str(admin.id)})
            
            # Update last login
            admin.last_login = datetime.utcnow()
            session.add(admin)
            session.commit()

            return AdminToken(
                access_token=access_token,
                token_type="bearer",
                admin=AdminResponse(
                    id=admin.id,
                    username=admin.username,
                    email=admin.email,
                    name=admin.name,
                    role=admin.role,
                    is_active=admin.is_active,
                    two_factor_enabled=admin.two_factor_enabled,
                    last_login=admin.last_login,
                    created_at=admin.created_at
                )
            )

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in admin login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during login. Please try again."
        )

# Admin 2FA Verification Endpoint (Step 2: 2FA Code)
@admin_router.post("/verify-2fa", response_model=AdminToken)
def admin_verify_2fa(admin_2fa: Admin2FAVerify, session: Session = Depends(get_session)):
    try:
        # Find valid 2FA token
        token_record = session.exec(
            select(Admin2FAToken)
            .where(Admin2FAToken.token == admin_2fa.token)
            .where(Admin2FAToken.is_used == False)
            .where(Admin2FAToken.expires_at > datetime.utcnow())
        ).first()

        if not token_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Invalid or expired 2FA token"
            )

        # Get admin
        admin = session.exec(select(Admin).where(Admin.id == token_record.admin_id)).first()
        if not admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="Admin not found"
            )

        # Verify 2FA code
        if not verify_2fa_code(admin.two_factor_secret, admin_2fa.two_factor_code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Invalid 2FA code"
            )

        # Mark token as used
        token_record.is_used = True
        session.add(token_record)

        # Create access token
        access_token = create_admin_access_token(data={"sub": str(admin.id)})
        
        # Update last login
        admin.last_login = datetime.utcnow()
        session.add(admin)
        session.commit()

        return AdminToken(
            access_token=access_token,
            token_type="bearer",
            admin=AdminResponse(
                id=admin.id,
                username=admin.username,
                email=admin.email,
                name=admin.name,
                role=admin.role,
                is_active=admin.is_active,
                two_factor_enabled=admin.two_factor_enabled,
                last_login=admin.last_login,
                created_at=admin.created_at
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in 2FA verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during 2FA verification. Please try again."
        )

# Setup 2FA for Admin
@admin_router.post("/setup-2fa", response_model=Admin2FASetup)
def setup_2fa(current_admin: Admin = Depends(get_current_admin), session: Session = Depends(get_session)):
    try:
        # Generate new 2FA secret
        secret = generate_2fa_secret()
        
        # Generate QR code
        qr_code_url = generate_2fa_qr_code(secret, current_admin.username)
        
        # Update admin with 2FA secret (but don't enable yet)
        current_admin.two_factor_secret = secret
        session.add(current_admin)
        session.commit()

        return Admin2FASetup(
            secret_key=secret,
            qr_code_url=qr_code_url
        )

    except Exception as e:
        print(f"Error in 2FA setup: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during 2FA setup. Please try again."
        )

# Enable 2FA for Admin
@admin_router.post("/enable-2fa")
def enable_2fa(two_factor_code: str, current_admin: Admin = Depends(get_current_admin), session: Session = Depends(get_session)):
    try:
        if not current_admin.two_factor_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="2FA not set up. Please set up 2FA first."
            )

        # Verify the 2FA code
        if not verify_2fa_code(current_admin.two_factor_secret, two_factor_code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Invalid 2FA code"
            )

        # Enable 2FA
        current_admin.two_factor_enabled = True
        session.add(current_admin)
        session.commit()

        return {"message": "2FA enabled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in enabling 2FA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while enabling 2FA. Please try again."
        )

# Disable 2FA for Admin
@admin_router.post("/disable-2fa")
def disable_2fa(two_factor_code: str, current_admin: Admin = Depends(get_current_admin), session: Session = Depends(get_session)):
    try:
        if not current_admin.two_factor_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="2FA is not enabled"
            )

        # Verify the 2FA code
        if not verify_2fa_code(current_admin.two_factor_secret, two_factor_code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Invalid 2FA code"
            )

        # Disable 2FA
        current_admin.two_factor_enabled = False
        current_admin.two_factor_secret = None
        session.add(current_admin)
        session.commit()

        return {"message": "2FA disabled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in disabling 2FA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while disabling 2FA. Please try again."
        )

# Get current admin profile
@admin_router.get("/me", response_model=AdminResponse)
def get_current_admin_profile(current_admin: Admin = Depends(get_current_admin)):
    return AdminResponse(
        id=current_admin.id,
        username=current_admin.username,
        email=current_admin.email,
        name=current_admin.name,
        role=current_admin.role,
        is_active=current_admin.is_active,
        two_factor_enabled=current_admin.two_factor_enabled,
        last_login=current_admin.last_login,
        created_at=current_admin.created_at
    ) 