#!/usr/bin/env python3
"""
Database connection diagnostic script
"""

import os
import sys
import psycopg2
from sqlmodel import create_engine, Session, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_environment():
    """Check if required environment variables are set"""
    print("🔍 Checking environment variables...")
    
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable is not set")
        return False
    
    print(f"✅ DATABASE_URL is set")
    print(f"   Database: {database_url.split('@')[1] if '@' in database_url else 'Unknown'}")
    return True

def test_direct_connection():
    """Test direct psycopg2 connection"""
    print("\n🔍 Testing direct psycopg2 connection...")
    
    try:
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ DATABASE_URL not available")
            return False
        
        # Convert to psycopg2 format
        if database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql://", 1)
        
        print("   Connecting to database...")
        conn = psycopg2.connect(database_url)
        
        # Test basic query
        cur = conn.cursor()
        cur.execute("SELECT version();")
        version = cur.fetchone()
        print(f"   ✅ Connected successfully!")
        print(f"   PostgreSQL version: {version[0]}")
        
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Connection failed: {e}")
        return False

def test_sqlmodel_connection():
    """Test SQLModel connection"""
    print("\n🔍 Testing SQLModel connection...")
    
    try:
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ DATABASE_URL not available")
            return False
        
        # Ensure correct format for SQLModel
        if database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)
        
        print("   Creating SQLModel engine...")
        engine = create_engine(
            database_url,
            echo=False,  # Set to True for debugging
            pool_pre_ping=True,  # Test connections before use
            pool_recycle=300,  # Recycle connections every 5 minutes
        )
        
        print("   Testing connection with SQLModel...")
        with Session(engine) as session:
            result = session.exec(text("SELECT 1 as test")).first()
            print(f"   ✅ SQLModel connection successful! Test result: {result}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ SQLModel connection failed: {e}")
        return False

def check_table_structure():
    """Check if required tables and columns exist"""
    print("\n🔍 Checking table structure...")
    
    try:
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("❌ DATABASE_URL not available")
            return False
        
        # Ensure correct format
        if database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql+psycopg2://", 1)
        
        engine = create_engine(database_url, echo=False)
        
        with Session(engine) as session:
            # Check user table
            print("   Checking user table...")
            result = session.exec(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'user'
                ORDER BY column_name
            """)).all()
            
            if result:
                print("   ✅ User table exists with columns:")
                for row in result:
                    print(f"     - {row[0]} ({row[1]})")
                
                # Check for required columns
                columns = [row[0] for row in result]
                required_columns = ['id', 'email', 'hashed_password', 'name', 'referral_code', 'referred_by']
                missing_columns = [col for col in required_columns if col not in columns]
                
                if missing_columns:
                    print(f"   ⚠️  Missing columns: {missing_columns}")
                else:
                    print("   ✅ All required columns present")
            else:
                print("   ❌ User table not found")
            
            # Check completeordermodel table
            print("   Checking completeordermodel table...")
            result = session.exec(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'completeordermodel'
                ORDER BY column_name
            """)).all()
            
            if result:
                print("   ✅ CompleteOrderModel table exists with columns:")
                for row in result:
                    print(f"     - {row[0]} ({row[1]})")
                
                # Check for completed_at column
                columns = [row[0] for row in result]
                if 'completed_at' not in columns:
                    print("   ⚠️  Missing completed_at column")
                else:
                    print("   ✅ completed_at column present")
            else:
                print("   ❌ CompleteOrderModel table not found")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking table structure: {e}")
        return False

def run_migration_if_needed():
    """Run migration if needed"""
    print("\n🔧 Running migration if needed...")
    
    try:
        from run_migration import run_migration
        run_migration()
        return True
    except Exception as e:
        print(f"   ❌ Migration failed: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔧 Database Connection Diagnostic")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please set DATABASE_URL.")
        sys.exit(1)
    
    # Test connections
    direct_ok = test_direct_connection()
    sqlmodel_ok = test_sqlmodel_connection()
    
    if not direct_ok:
        print("\n❌ Direct database connection failed. Check your DATABASE_URL and network connectivity.")
        sys.exit(1)
    
    if not sqlmodel_ok:
        print("\n❌ SQLModel connection failed. Check your database configuration.")
        sys.exit(1)
    
    # Check table structure
    structure_ok = check_table_structure()
    
    if not structure_ok:
        print("\n⚠️  Table structure issues detected. Running migration...")
        migration_ok = run_migration_if_needed()
        if not migration_ok:
            print("\n❌ Migration failed. Please check the error messages above.")
            sys.exit(1)
    
    print("\n✅ Database diagnostic completed successfully!")
    print("   Your database connection should be working properly now.")

if __name__ == "__main__":
    main() 