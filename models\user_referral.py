from sqlmodel import SQLModel, Field, Relationship
from datetime import datetime
from typing import Optional
import uuid

def generate_referral_code():
    """Generate a unique referral code"""
    return str(uuid.uuid4())[:8].upper()

class UserReferral(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id", unique=True)
    referral_code: str = Field(default_factory=generate_referral_code, unique=True)
    total_points: float = Field(default=0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationship to User
    user: Optional["User"] = Relationship(back_populates="user_referral") 