#!/usr/bin/env python3
"""
Complete database fix script - adds all missing columns without deleting anything
"""

import os
import sys
import psycopg2
from sqlmodel import create_engine, Session, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get database URL
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    print("❌ DATABASE_URL environment variable is not set")
    sys.exit(1)

# Ensure correct PostgreSQL format
if DATABASE_URL.startswith("postgres://"):
    DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql+psycopg2://", 1)

def add_column_if_not_exists(session, table_name, column_name, column_definition):
    """Add a column if it doesn't exist"""
    try:
        # Check if column exists
        result = session.exec(text(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '{table_name}' AND column_name = '{column_name}'
        """)).first()
        
        if not result:
            print(f"   Adding '{column_name}' to {table_name}...")
            session.exec(text(f'ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}'))
            session.commit()
            print(f"   ✅ Added '{column_name}' to {table_name}")
            return True
        else:
            print(f"   ✅ '{column_name}' already exists in {table_name}")
            return False
    except Exception as e:
        print(f"   ❌ Error adding {column_name} to {table_name}: {e}")
        session.rollback()
        return False

def fix_database():
    """Fix all missing columns in the database"""
    
    print("🔧 Complete Database Fix")
    print("=" * 50)
    print("Adding all missing columns without deleting anything...")
    
    # Create engine
    engine = create_engine(
        DATABASE_URL,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    with Session(engine) as session:
        try:
            # 1. Fix User table
            print("\n1. Fixing User table...")
            user_columns = [
                ("referral_code", "VARCHAR UNIQUE DEFAULT substr(md5(random()::text), 1, 8)"),
                ("referred_by", "VARCHAR"),
                ("total_points", "FLOAT DEFAULT 0"),
            ]
            
            for column_name, column_def in user_columns:
                add_column_if_not_exists(session, '"user"', column_name, column_def)

            # 2. Fix OrderModel table
            print("\n2. Fixing OrderModel table...")
            ordermodel_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in ordermodel_columns:
                add_column_if_not_exists(session, "ordermodel", column_name, column_def)

            # 3. Fix CompleteOrderModel table
            print("\n3. Fixing CompleteOrderModel table...")
            completeordermodel_columns = [
                ("completed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in completeordermodel_columns:
                add_column_if_not_exists(session, "completeordermodel", column_name, column_def)

            # 4. Fix FailOrder table
            print("\n4. Fixing FailOrder table...")
            failorder_columns = [
                ("failed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in failorder_columns:
                add_column_if_not_exists(session, "failorder", column_name, column_def)

            # 5. Fix RejectOrder table
            print("\n5. Fixing RejectOrder table...")
            rejectorder_columns = [
                ("rejected_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in rejectorder_columns:
                add_column_if_not_exists(session, "rejectorder", column_name, column_def)

            # 6. Fix PassOrder table
            print("\n6. Fixing PassOrder table...")
            passorder_columns = [
                ("pass_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in passorder_columns:
                add_column_if_not_exists(session, "passorder", column_name, column_def)

            # 7. Fix OrderTimeline table
            print("\n7. Fixing OrderTimeline table...")
            ordertimeline_columns = [
                ("event_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in ordertimeline_columns:
                add_column_if_not_exists(session, "ordertimeline", column_name, column_def)

            # 8. Fix OrderImage table
            print("\n8. Fixing OrderImage table...")
            orderimage_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_def in orderimage_columns:
                add_column_if_not_exists(session, "orderimage", column_name, column_def)

            # 9. Fix LiveAccount table
            print("\n9. Fixing LiveAccount table...")
            liveaccount_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("profit_share", "FLOAT DEFAULT 80.0"),
                ("status", "VARCHAR DEFAULT 'active'"),
            ]
            
            for column_name, column_def in liveaccount_columns:
                add_column_if_not_exists(session, "liveaccount", column_name, column_def)

            # 10. Fix Stage2Account table
            print("\n10. Fixing Stage2Account table...")
            stage2account_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("profit_target", "FLOAT"),
                ("status", "VARCHAR DEFAULT 'active'"),
            ]
            
            for column_name, column_def in stage2account_columns:
                add_column_if_not_exists(session, "stage2account", column_name, column_def)

            # 11. Fix Certificate table
            print("\n11. Fixing Certificate table...")
            certificate_columns = [
                ("issue_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("profit_target", "FLOAT"),
            ]
            
            for column_name, column_def in certificate_columns:
                add_column_if_not_exists(session, "certificate", column_name, column_def)

            # 12. Fix DateTimeModel table
            print("\n12. Fixing DateTimeModel table...")
            datetimemodel_columns = [
                ("updated_at", "TIMESTAMP"),
            ]
            
            for column_name, column_def in datetimemodel_columns:
                add_column_if_not_exists(session, "datetimemodel", column_name, column_def)

            print("\n✅ All database fixes completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Database fix failed: {e}")
            session.rollback()
            raise

def verify_fix():
    """Verify that all columns were added successfully"""
    print("\n🔍 Verifying database fix...")
    
    engine = create_engine(DATABASE_URL, echo=False)
    
    with Session(engine) as session:
        try:
            # Test critical queries that were failing
            print("\n   Testing critical queries...")
            
            # Test user table query
            try:
                result = session.exec(text("SELECT id, email, referral_code, referred_by FROM \"user\" LIMIT 1")).all()
                print("   ✅ User table query works")
            except Exception as e:
                print(f"   ❌ User table query failed: {e}")
            
            # Test failorder table query
            try:
                result = session.exec(text("SELECT id, order_id, reason FROM failorder LIMIT 1")).all()
                print("   ✅ FailOrder table query works")
            except Exception as e:
                print(f"   ❌ FailOrder table query failed: {e}")
            
            # Test completeordermodel table query
            try:
                result = session.exec(text("SELECT id, order_id, completed_at FROM completeordermodel LIMIT 1")).all()
                print("   ✅ CompleteOrderModel table query works")
            except Exception as e:
                print(f"   ❌ CompleteOrderModel table query failed: {e}")
            
            print("\n   ✅ All critical queries are working!")
            
        except Exception as e:
            print(f"   ❌ Verification failed: {e}")
            raise

def main():
    """Main function"""
    try:
        print("🚀 Starting complete database fix...")
        print(f"Database: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'Unknown'}")
        
        fix_database()
        verify_fix()
        
        print("\n🎉 Database fix completed successfully!")
        print("   Your application should now work without any column errors.")
        print("   All existing data has been preserved.")
        
    except Exception as e:
        print(f"\n❌ Database fix failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 