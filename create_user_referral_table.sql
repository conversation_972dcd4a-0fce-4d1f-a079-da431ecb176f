-- Create new user_referral table to store referral codes and points
CREATE TABLE IF NOT EXISTS user_referral (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE,
    referral_code VARCHAR(255) UNIQUE NOT NULL,
    total_points FLOAT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_user_referral_user_id ON user_referral(user_id);
CREATE INDEX IF NOT EXISTS idx_user_referral_code ON user_referral(referral_code);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_referral_updated_at 
    BEFORE UPDATE ON user_referral 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default records for existing users (if any)
-- This will create referral records for users who don't have them yet
INSERT INTO user_referral (user_id, referral_code, total_points)
SELECT 
    u.id,
    'REF' || LPAD(u.id::text, 6, '0'), -- Generate referral code based on user ID
    0 -- Default points
FROM "user" u
WHERE NOT EXISTS (
    SELECT 1 FROM user_referral ur WHERE ur.user_id = u.id
);

-- Display the results
SELECT 'User Referral Table Created Successfully!' as status;
SELECT COUNT(*) as total_users FROM "user";
SELECT COUNT(*) as total_referral_records FROM user_referral; 