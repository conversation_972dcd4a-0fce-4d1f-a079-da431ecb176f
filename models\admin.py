from sqlmodel import SQLModel, Field, Relationship
from datetime import datetime
from typing import Optional
from pydantic import EmailStr

class Admin(SQLModel, table=True):
    __tablename__ = "admins"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    username: str = Field(unique=True, index=True)
    email: EmailStr = Field(unique=True, index=True)
    hashed_password: str
    name: str
    role: str = Field(default="admin")  # admin, super_admin
    is_active: bool = Field(default=True)
    two_factor_secret: Optional[str] = Field(default=None)  # For 2FA
    two_factor_enabled: bool = Field(default=False)
    last_login: Optional[datetime] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

class Admin2FAToken(SQLModel, table=True):
    __tablename__ = "admin_2fa_tokens"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    admin_id: int = Field(foreign_key="admins.id")
    token: str = Field(index=True, unique=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    is_used: bool = Field(default=False) 