from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlmodel import SQLModel, Field, Session, select, delete
from schemas.user import UserCreate, UserResponse, UserWithVerification, Token, UserCreateWithRecaptcha, UserLoginWithRecaptcha, ForgotPasswordRequest
from models.user import User
from models.user_referral import UserReferral
from models.user_verification import UserVerification
from models.referral import ReferralPoints, PointsTransactionType
from auth import get_password_hash, verify_password, create_access_token, get_current_user
from db import get_session
from fastapi.responses import StreamingResponse
import io
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from pydantic import EmailStr
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import secrets
from datetime import datetime, timedelta
import random
from sqlalchemy.orm import selectinload
from utils.recaptcha import verify_recaptcha
from sqlalchemy import func

auth_router = APIRouter(prefix = "/auth")

REFERRAL_BONUS_POINTS = 100  # Points awarded for successful referral

# Function to send welcome email
def send_email(to_email, subject, body):
    from_email = "<EMAIL>"  # Updated email
    from_password = "Fundedhorizon@123"   # Use your email password

    msg = MIMEMultipart()
    msg["From"] = from_email
    msg["To"] = to_email
    msg["Subject"] = subject
    msg.attach(MIMEText(body, "html"))

    # Use Hostinger's SMTP server
    with smtplib.SMTP_SSL("smtp.hostinger.com", 465) as server:  # SSL on port 465
        server.login(from_email, from_password)
        server.sendmail(from_email, to_email, msg.as_string())

# Email Verification Token Model
class EmailVerificationToken(SQLModel, table=True):
    __tablename__ = "email_verification_tokens"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")  # Fixed: Changed from "users.id" to "user.id"
    code: str = Field(index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime = Field()
    is_used: bool = Field(default=False)

# Password Reset Token Model
class PasswordResetToken(SQLModel, table=True):
    __tablename__ = "password_reset_tokens"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    token: str = Field(index=True, unique=True)  # Ensure tokens are unique
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime = Field()
    is_used: bool = Field(default=False)

def generate_verification_code():
    # Generate a 6-digit code
    return ''.join([str(random.randint(0, 9)) for _ in range(6)])

def generate_reset_token():
    # Generate a secure random token
    return secrets.token_urlsafe(32)

def send_password_reset_email(to_email: str, reset_token: str):
    subject = "Password Reset - Funded Horizon"
    reset_link = f"https://www.fundedhorizon.com/reset-password?token={reset_token}"
    body = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset - Funded Horizon</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0a1a30; color: #e6e6e6;">
        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
                <td style="padding: 0;">
                    <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #153a6a 0%, #0a1a30 100%); border: 1px solid #1e3a5f;">
                        <!-- Header with Logo -->
                        <tr>
                            <td style="padding: 25px 30px 15px; text-align: center;">
                                <div style="display: inline-block; background: linear-gradient(145deg, #0a2240 0%, #153a6a 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(255, 130, 0, 0.3);">
                                    <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FUNDED<span style="color: #ff8200;">HORIZON</span></h1>
                                </div>
                            </td>
                        </tr>

                        <!-- Main Content -->
                        <tr>
                            <td style="padding: 30px 30px 20px;">
                                <h2 style="color: #ff8200; margin: 0 0 20px; font-size: 24px;">Password Reset Request</h2>

                                <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">
                                    We received a request to reset your password. If you didn't make this request, you can safely ignore this email.
                                </p>

                                <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">
                                    To reset your password, click the button below:
                                </p>

                                <!-- Reset Password Button -->
                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 30px 0;">
                                    <tr>
                                        <td style="text-align: center;">
                                            <a href="https://www.fundedhorizon.com/" style="display: inline-block; background: linear-gradient(90deg, #ff8200, #ff9a40); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: bold; font-size: 16px; text-transform: uppercase; letter-spacing: 1px;">RESET PASSWORD</a>
                                        </td>
                                    </tr>
                                </table>

                                <p style="color: #e6e6e6; margin: 20px 0; font-size: 14px; line-height: 1.6;">
                                    If the button doesn't work, copy and paste the following link into your browser:
                                </p>

                                <p style="background: rgba(255, 130, 0, 0.1); border: 1px solid rgba(255, 130, 0, 0.3); padding: 10px; margin: 20px 0; font-size: 14px; line-height: 1.6; border-radius: 5px; word-break: break-all;">
                                    <a href="{reset_link}" style="color: #ff8200; text-decoration: none;">{reset_link}</a>
                                </p>

                                <p style="color: #e6e6e6; margin: 20px 0; font-size: 14px; line-height: 1.6;">
                                    This password reset link will expire in 1 hour for security reasons.
                                </p>

                                <p style="color: #e6e6e6; margin: 20px 0; font-size: 16px; line-height: 1.6; font-weight: bold;">
                                    If you didn't request a password reset, please ignore this email or contact support if you have concerns.
                                </p>
                            </td>
                        </tr>

                        <!-- Footer -->
                        <tr>
                            <td style="background-color: #061325; padding: 20px; border-top: 1px solid #1e3a5f;">
                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                    <tr>
                                        <td style="text-align: center; color: #a3c2ff; font-size: 14px;">
                                            <p style="margin: 0 0 10px;">© 2025 Funded Horizon Prop Trading. All rights reserved.</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
    send_email(to_email, subject, body)

def send_verification_email(to_email: str, verification_code: str):
    subject = "Your Verification Code - Funded Horizon"
    body = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Verification Code - Funded Horizon</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0a1a30; color: #e6e6e6;">
        <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
                <td style="padding: 0;">
                    <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #153a6a 0%, #0a1a30 100%); border: 1px solid #1e3a5f;">
                        <!-- Header with Logo -->
                        <tr>
                            <td style="padding: 25px 30px 15px; text-align: center;">
                                <div style="display: inline-block; background: linear-gradient(145deg, #0a2240 0%, #153a6a 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(255, 130, 0, 0.3);">
                                    <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FUNDED<span style="color: #ff8200;">HORIZON</span></h1>
                                </div>
                            </td>
                        </tr>

                        <!-- Main Content -->
                        <tr>
                            <td style="padding: 30px 30px 20px;">
                                <h2 style="color: #ff8200; margin: 0 0 20px; font-size: 24px;">Your Verification Code</h2>

                                <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">
                                    Thank you for registering with Funded Horizon. To verify your email address, please use the following verification code:
                                </p>

                                <!-- Verification Code Box -->
                                <div style="background: rgba(255, 130, 0, 0.1); border: 2px solid #ff8200; padding: 20px; margin: 30px auto; max-width: 300px; text-align: center; border-radius: 10px;">
                                    <h1 style="color: #ff8200; font-size: 32px; letter-spacing: 5px; margin: 0; font-family: monospace;">{verification_code}</h1>
                                </div>

                                <p style="color: #e6e6e6; margin: 20px 0; font-size: 14px; line-height: 1.6;">
                                    This verification code will expire in 24 hours. If you didn't create an account with Funded Horizon, please ignore this email.
                                </p>

                                <p style="color: #e6e6e6; margin: 20px 0; font-size: 16px; line-height: 1.6; font-weight: bold;">
                                    For security reasons, please do not share this code with anyone.
                                </p>
                            </td>
                        </tr>

                        <!-- Footer -->
                        <tr>
                            <td style="background-color: #061325; padding: 20px; border-top: 1px solid #1e3a5f;">
                                <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                    <tr>
                                        <td style="text-align: center; color: #a3c2ff; font-size: 14px;">
                                            <p style="margin: 0 0 10px;">© 2025 Funded Horizon Prop Trading. All rights reserved.</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
    send_email(to_email, subject, body)

# Signup Endpoint
@auth_router.post("/signup", response_model=UserWithVerification)
async def signup(user_create: UserCreateWithRecaptcha, request: Request, session: Session = Depends(get_session)):
    # Verify reCAPTCHA
    client_ip = request.client.host if request.client else None
    verify_recaptcha(user_create.recaptcha_token, client_ip)
    
    # Extract user data without recaptcha_token
    user_data = UserCreate(
        username=user_create.username,
        email=user_create.email,
        password=user_create.password,
        name=user_create.name,
        phone_no=user_create.phone_no,
        country=user_create.country,
        address=user_create.address,
        referral_code=user_create.referral_code
    )
    try:
        # Check if user already exists
        user_exists = session.exec(select(User).where(User.email == user_data.email)).first()
        if user_exists:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered")

        # Validate referral code if provided
        referrer = None
        if user_data.referral_code:
            # Look up referrer by referral code in user_referral table
            referrer_record = session.exec(
                select(UserReferral).where(UserReferral.referral_code == user_data.referral_code)
            ).first()
            if not referrer_record:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid referral code")
            
            # Get the referrer user
            referrer = session.exec(select(User).where(User.id == referrer_record.user_id)).first()

        # Hash the password and create user
        hashed_password = get_password_hash(user_data.password)
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
            name=user_data.name,
            country=user_data.country,
            phone_no=user_data.phone_no,
            address=user_data.address,
            referred_by=user_data.referral_code if user_data.referral_code else None
        )
        session.add(new_user)
        session.commit()
        session.refresh(new_user)

        # Create user referral record
        user_referral = UserReferral(
            user_id=new_user.id,
            referral_code=f"REF{new_user.id:06d}",  # Generate referral code based on user ID
            total_points=0
        )
        session.add(user_referral)
        session.commit()

        # Handle referral bonus if applicable
        if referrer:
            # Get referrer's referral record
            referrer_referral = session.exec(
                select(UserReferral).where(UserReferral.user_id == referrer.id)
            ).first()
            
            if referrer_referral:
                # Create points transaction for referrer
                referrer_points = ReferralPoints(
                    user_id=referrer.id,
                    points=REFERRAL_BONUS_POINTS,
                    transaction_type=PointsTransactionType.REFERRAL_BONUS,
                    amount=REFERRAL_BONUS_POINTS,
                    description=f"Referral bonus for user {new_user.email}",
                    status="completed"
                )
                referrer_referral.total_points += REFERRAL_BONUS_POINTS
                session.add(referrer_points)
                session.add(referrer_referral)
                session.commit()

        try:
            # Create verification record
            verification = UserVerification(user_id=new_user.id)
            session.add(verification)
            session.commit()

            # Generate verification code
            verification_code = generate_verification_code()
            verification_token = EmailVerificationToken(
                user_id=new_user.id,
                code=verification_code,
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )
            session.add(verification_token)
            session.commit()

            # Send verification email with code
            try:
                send_verification_email(new_user.email, verification_code)
            except Exception as e:
                print(f"Error sending verification email: {str(e)}")
                # Continue even if email fails, as user is created

            # Send welcome email
            try:
                send_welcome_email(new_user)
            except Exception as e:
                print(f"Error sending welcome email: {str(e)}")
                # Continue even if email fails, as user is created

        except Exception as e:
            print(f"Error in verification process: {str(e)}")
            # Continue as user is created successfully

        # Create response with verification status
        user_with_verification = UserWithVerification.model_validate(new_user)
        user_with_verification.is_verified = False
        return user_with_verification

    except Exception as e:
        session.rollback()
        print(f"Error in signup process: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during signup. Please try again."
        )

def send_welcome_email(user: User):
    subject = "Welcome to Funded Horizon"
    body = f"""
    <!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Funded Horizon!</title>
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Arial, sans-serif; background-color: #0a1a30; color: #e6e6e6;">
    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
        <tr>
            <td style="padding: 0;">
                <table role="presentation" cellpadding="0" cellspacing="0" width="650" style="border-collapse: collapse; margin: 0 auto; background: linear-gradient(145deg, #153a6a 0%, #0a1a30 100%); border: 1px solid #1e3a5f;">

                    <!-- Header with Logo -->
                    <tr>
                        <td style="padding: 25px 30px 15px; text-align: center;">
                            <!-- Logo with glowing effect -->
                            <div style="display: inline-block; background: linear-gradient(145deg, #0a2240 0%, #153a6a 100%); padding: 20px; border-radius: 15px; box-shadow: 0 0 25px rgba(255, 130, 0, 0.3);">
                                <h1 style="color: #ffffff; margin: 0; font-size: 32px; letter-spacing: 2px; text-transform: uppercase; font-weight: 800;">FUNDED<span style="color: #ff8200;">HORIZON</span></h1>
                                <p style="color: #a3c2ff; margin: 5px 0 0; font-size: 14px; letter-spacing: 3px; text-transform: uppercase;">PROP TRADING</p>
                            </div>
                        </td>
                    </tr>

                    <!-- Welcome Banner -->
                    <tr>
                        <td style="padding: 0; position: relative;">
                            <div style="background-color: #061325; height: 150px; position: relative; overflow: hidden;">
                                <!-- Overlay with text -->
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(145deg, rgba(10, 34, 64, 0.85) 0%, rgba(21, 58, 106, 0.85) 100%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
                                    <div style="background: rgba(6, 19, 37, 0.7); padding: 25px 40px; border-radius: 10px; border: 1px solid rgba(255, 130, 0, 0.3);">
                                        <h2 style="color: #ffffff; margin: 0; font-size: 28px; letter-spacing: 1px; text-transform: uppercase; font-weight: 700;">WELCOME ABOARD</h2>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <!-- Main Content -->
                    <tr>
                        <td style="padding: 30px 30px 20px;">
                            <h3 style="color: #ff8200; margin: 0 0 15px; font-size: 22px; letter-spacing: 1px;">Dear {user.name},</h3>

                            <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">Thank you for joining <span style="color: #ff8200; font-weight: bold;">Funded Horizon</span>. We're excited to have you as part of our trading community. Your account has been successfully created and is now ready to use.</p>

                            <p style="color: #e6e6e6; margin: 0 0 20px; font-size: 16px; line-height: 1.6;">Here's what you can do next:</p>

                            <!-- Next Steps -->
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0; background: linear-gradient(145deg, #153a6a 0%, #0a1a30 100%); border-radius: 10px; border: 1px solid #1e3a5f;">
                                <tr>
                                    <td style="padding: 20px;">
                                        <ol style="color: #e6e6e6; margin: 0; padding-left: 20px; line-height: 1.6;">
                                            <li style="margin-bottom: 10px;"><span style="color: #ffffff; font-weight: bold;">Complete Your Profile</span> - Verify your email and add your trading preferences.</li>
                                            <li style="margin-bottom: 10px;"><span style="color: #ffffff; font-weight: bold;">Explore Available Challenges</span> - Browse through our range of trading challenges.</li>
                                            <li><span style="color: #ffffff; font-weight: bold;">Purchase Your First Challenge</span> - Select a trading challenge that matches your skill level.</li>
                                        </ol>
                                    </td>
                                </tr>
                            </table>

                            <!-- CTA Button -->
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin: 25px 0;">
                                <tr>
                                    <td style="text-align: center;">
                                        <a href="https://www.fundedhorizon.com/" style="display: inline-block; background: linear-gradient(90deg, #ff8200, #ff9a40); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: bold; font-size: 16px; text-transform: uppercase; letter-spacing: 1px;">ACCESS YOUR ACCOUNT</a>
                                    </td>
                                </tr>
                            </table>

                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse; margin-top: 25px;">
                                <tr>
                                    <td style="padding: 0;">
                                        <p style="color: #e6e6e6; margin: 0; font-size: 16px; line-height: 1.6;">Happy Trading,</p>
                                        <p style="color: #ff8200; margin: 10px 0 0; font-size: 18px; font-weight: bold;">The Funded Horizon Team</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #061325; padding: 20px; border-top: 1px solid #1e3a5f;">
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                                <tr>
                                    <td style="text-align: center; color: #a3c2ff; font-size: 14px;">
                                        <p style="margin: 0 0 10px;">© 2025 Funded Horizon Prop Trading. All rights reserved.</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    """
    send_email(user.email, subject, body)

@auth_router.post("/verify-email")
def verify_email(email: str, code: str, session: Session = Depends(get_session)):
    # Get user
    user = session.exec(select(User).where(User.email == email)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Get verification token
    token = session.exec(
        select(EmailVerificationToken)
        .where(EmailVerificationToken.user_id == user.id)
        .where(EmailVerificationToken.code == code)
        .where(EmailVerificationToken.is_used == False)
        .where(EmailVerificationToken.expires_at > datetime.utcnow())
    ).first()

    if not token:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or expired verification code")

    # Update verification status
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
    if not verification:
        verification = UserVerification(user_id=user.id)
        session.add(verification)

    verification.is_verified = True
    verification.verified_at = datetime.utcnow()

    # Mark token as used
    token.is_used = True

    session.commit()
    return {"message": "Email verified successfully"}

@auth_router.get("/user/verification-status/{user_id}")
def get_verification_status(user_id: int, session: Session = Depends(get_session)):
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == user_id)).first()
    if not verification:
        return {"is_verified": False}
    return {"is_verified": verification.is_verified}

@auth_router.post("/resend-verification")
def resend_verification(email: str, session: Session = Depends(get_session)):
    # Get user
    user = session.exec(select(User).where(User.email == email)).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if email is already verified
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
    is_verified = verification.is_verified if verification else False

    if is_verified:
        raise HTTPException(status_code=400, detail="Email is already verified")

    # Generate new verification code
    verification_code = generate_verification_code()
    verification_token = EmailVerificationToken(
        user_id=user.id,
        code=verification_code,
        expires_at=datetime.utcnow() + timedelta(hours=24)
    )
    session.add(verification_token)
    session.commit()

    # Send verification email with new code
    send_verification_email(user.email, verification_code)

    return {"message": "Verification code sent successfully"}

@auth_router.put("/user/{user_id}/update-password", status_code=status.HTTP_200_OK)
def update_password(user_id: int, new_password: str, session: Session = Depends(get_session)):
    # Fetch the user by ID
    user = session.exec(select(User).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Verify if the new password is the same as the existing password
    if verify_password(new_password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="New password cannot be the same as the current password")

    # Hash the new password and update the existing column
    user.hashed_password = get_password_hash(new_password)
    session.add(user)
    session.commit()
    session.refresh(user)

    return {"message": "Password updated successfully"}

@auth_router.get("/user/{user_id}/password", status_code=status.HTTP_200_OK)
def get_password(user_id: int, session: Session = Depends(get_session)):
        # Fetch the user by ID
        user = session.exec(select(User).where(User.id == user_id)).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        return {"hashed_password": user.hashed_password}

# Login Endpoint with reCAPTCHA
#NOTE: You have to tell the frontend developer that he has to send the email in the key of username and should ask from the user the email but put it against the username key in the header.
@auth_router.post("/login", response_model=Token)
async def login(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends(OAuth2PasswordRequestForm)], 
    recaptcha_token: str = Query(..., description="reCAPTCHA token"),
    request: Request = None,
    session: Session = Depends(get_session)
):
    # Verify reCAPTCHA
    client_ip = request.client.host if request.client else None
    verify_recaptcha(recaptcha_token, client_ip)
    
    db_user = session.exec(select(User).where(User.email == form_data.username)).first()
    if not db_user or not verify_password(form_data.password, db_user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid credentials")

    # Check if email is verified by querying the UserVerification table
    # We still check verification status but don't block login
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == db_user.id)).first()
    is_verified = verification.is_verified if verification else False

    # No longer blocking unverified users from logging in

    # Create JWT token
    access_token = create_access_token(data={"sub": str(db_user.id)})
    return {"access_token": access_token, "token_type": "bearer", "is_verified": is_verified}

# Login Endpoint with reCAPTCHA
@auth_router.post("/login-with-recaptcha", response_model=Token)
async def login_with_recaptcha(login_data: UserLoginWithRecaptcha, request: Request, session: Session = Depends(get_session)):
    # Verify reCAPTCHA
    client_ip = request.client.host if request.client else None
    verify_recaptcha(login_data.recaptcha_token, client_ip)
    
    # Authenticate user
    db_user = session.exec(select(User).where(User.email == login_data.email)).first()
    if not db_user or not verify_password(login_data.password, db_user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid credentials")

    # Check if email is verified by querying the UserVerification table
    # We still check verification status but don't block login
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == db_user.id)).first()
    is_verified = verification.is_verified if verification else False

    # No longer blocking unverified users from logging in

    # Create JWT token
    access_token = create_access_token(data={"sub": str(db_user.id)})
    return {"access_token": access_token, "token_type": "bearer", "is_verified": is_verified}

# Token Refresh Endpoint
@auth_router.post("/token/refresh", response_model=Token)
def refresh_token(current_user: User = Depends(get_current_user), session: Session = Depends(get_session)):
    # Check verification status
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == current_user.id)).first()
    is_verified = verification.is_verified if verification else False

    access_token = create_access_token(data={"sub": str(current_user.id)})
    return {"access_token": access_token, "token_type": "bearer", "is_verified": is_verified}

    # Get All Users Endpoint
@auth_router.get("/users", response_model=List[UserWithVerification])
def get_all_users(session: Session = Depends(get_session)):
    users = session.exec(select(User)).all()
    result = []
    for user in users:
        verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
        # Create a new UserWithVerification instance
        user_with_verification = UserWithVerification.model_validate(user)
        user_with_verification.is_verified = verification.is_verified if verification else False
        result.append(user_with_verification)
    return result

    # Get Current User Details Endpoint
@auth_router.get("/user/me", response_model=UserWithVerification)
def get_current_user_details(current_user: User = Depends(get_current_user), session: Session = Depends(get_session)):
    verification = session.exec(select(UserVerification).where(UserVerification.user_id == current_user.id)).first()
    # Create a new UserWithVerification instance
    user_with_verification = UserWithVerification.model_validate(current_user)
    user_with_verification.is_verified = verification.is_verified if verification else False
    return user_with_verification

# Forgot Password Endpoint - Request password reset
@auth_router.post("/forgot-password")
async def forgot_password(request_data: ForgotPasswordRequest, request: Request, session: Session = Depends(get_session)):
    # Verify reCAPTCHA
    client_ip = request.client.host if request.client else None
    verify_recaptcha(request_data.recaptcha_token, client_ip)
    
    # Find user by email
    user = session.exec(select(User).where(User.email == request_data.email)).first()
    if not user:
        # For security reasons, don't reveal that the email doesn't exist
        # Just return success message as if we sent the email
        return {"message": "If your email is registered, you will receive a password reset link."}

    # Generate reset token
    reset_token = generate_reset_token()

    # Create password reset token record
    token_record = PasswordResetToken(
        user_id=user.id,
        token=reset_token,
        expires_at=datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour
    )
    session.add(token_record)
    session.commit()

    # Send password reset email
    try:
        send_password_reset_email(user.email, reset_token)
    except Exception as e:
        print(f"Error sending password reset email: {str(e)}")
        # Continue even if email fails, as token is created

    return {"message": "If your email is registered, you will receive a password reset link."}

# Reset Password Endpoint - Reset password using token
@auth_router.post("/reset-password")
def reset_password(token: str, new_password: str, session: Session = Depends(get_session)):
    # Find valid token first
    token_record = session.exec(
        select(PasswordResetToken)
        .where(PasswordResetToken.token == token)
        .where(PasswordResetToken.is_used == False)
        .where(PasswordResetToken.expires_at > datetime.utcnow())
    ).first()

    if not token_record:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or expired token")

    # Find user based on the token's user_id
    user = session.exec(select(User).where(User.id == token_record.user_id)).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Verify if the new password is the same as the existing password
    if verify_password(new_password, user.hashed_password):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="New password cannot be the same as the current password")

    # Update password
    user.hashed_password = get_password_hash(new_password)

    # Mark token as used
    token_record.is_used = True

    session.add(user)
    session.add(token_record)
    session.commit()

    return {"message": "Password has been reset successfully"}

@auth_router.get("/user/all", response_model=List[UserWithVerification])
def get_all_users_new(session: Session = Depends(get_session)):
    try:
        users = session.exec(
            select(User).options(selectinload(User.user_referral))
        ).all()
        result = []
        for user in users:
            verification = session.exec(select(UserVerification).where(UserVerification.user_id == user.id)).first()
            user_with_verification = UserWithVerification.model_validate(user)
            user_with_verification.is_verified = verification.is_verified if verification else False
            result.append(user_with_verification)
        return result
    except Exception as e:
        print("Error in /user/all:", e)
        import traceback; traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@auth_router.delete("/user/delete-by-phone")
def delete_users_by_phone(phone_no: str, session: Session = Depends(get_session)):
    """
    Delete up to 1000 users (and related records) with the given phone number in one request.
    If more than 1000 users exist, only the first 1000 are deleted per request.
    """
    BATCH_SIZE = 1000
    users = session.exec(select(User).where(User.phone_no == phone_no).limit(BATCH_SIZE)).all()
    if not users:
        raise HTTPException(status_code=404, detail="No users found with this phone number")

    user_ids = [user.id for user in users]
    deleted_count = 0
    try:
        # Delete related records for each user
        for user_id in user_ids:
            session.exec(delete(UserVerification).where(UserVerification.user_id == user_id))
            session.exec(delete(UserReferral).where(UserReferral.user_id == user_id))
            session.exec(delete(PasswordResetToken).where(PasswordResetToken.user_id == user_id))
            session.exec(delete(EmailVerificationToken).where(EmailVerificationToken.user_id == user_id))
        # Delete users
        for user in users:
            session.delete(user)
            deleted_count += 1
        session.commit()
        # Correct way to count remaining users
        remaining = session.exec(select(func.count()).select_from(User).where(User.phone_no == phone_no)).one()
        remaining = remaining[0] if isinstance(remaining, tuple) else remaining
        msg = f"Deleted {deleted_count} user(s) with phone number {phone_no}."
        if remaining > 0:
            msg += f" {remaining} user(s) with this phone number remain. Run the endpoint again to delete more."
        return {"message": msg, "deleted": deleted_count, "remaining": remaining}
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting users: {str(e)}")

