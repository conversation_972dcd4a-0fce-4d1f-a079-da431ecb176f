import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, AsyncMock
import json
from app import app

client = TestClient(app)

# Test data for signup
test_signup_data = {
    "username": "testuser_recaptcha",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "name": "Test User reCAPTCHA",
    "phone_no": "1234567890",
    "country": "Test Country",
    "address": "Test Address",
    "recaptcha_token": "test_recaptcha_token_123"
}

# Test data for login
test_login_data = {
    "username": "<EMAIL>",  # OAuth2 form uses 'username' for email
    "password": "testpassword123"
}

# Test data for forgot password
test_forgot_password_data = {
    "email": "<EMAIL>",
    "recaptcha_token": "test_recaptcha_token_456"
}

@patch('utils.recaptcha.verify_recaptcha')
def test_signup_with_recaptcha_integration(mock_verify_recaptcha):
    mock_verify_recaptcha.return_value = True
    # ... rest of test ...

@patch('utils.recaptcha.verify_recaptcha')
def test_signup_with_recaptcha_success(mock_verify_recaptcha):
    """Test signup endpoint with successful reCAPTCHA verification"""
    # Mock successful reCAPTCHA verification
    mock_verify_recaptcha.return_value = AsyncMock(return_value=True)
    
    # Mock database operations to avoid actual database calls
    with patch('routes.auth.get_session') as mock_session:
        # Mock session to return a mock session object
        mock_db_session = AsyncMock()
        mock_session.return_value = mock_db_session
        
        # Mock user existence check (user doesn't exist)
        mock_db_session.exec.return_value.first.return_value = None
        
        # Mock user creation
        mock_db_session.add.return_value = None
        mock_db_session.commit.return_value = None
        mock_db_session.refresh.return_value = None
        
        response = client.post("/auth/signup", json=test_signup_data)
        
        # Verify reCAPTCHA was called with correct parameters
        mock_verify_recaptcha.assert_called_once_with("test_recaptcha_token_123", None)
        
        # Check response status (should be 200 for success or 500 for database error)
        assert response.status_code in [200, 500]

@patch('utils.recaptcha.verify_recaptcha')
def test_signup_with_recaptcha_failure(mock_verify_recaptcha):
    """Test signup endpoint with failed reCAPTCHA verification"""
    # Mock failed reCAPTCHA verification
    mock_verify_recaptcha.side_effect = Exception("reCAPTCHA verification failed")
    
    response = client.post("/auth/signup", json=test_signup_data)
    
    # Should return an error when reCAPTCHA fails
    assert response.status_code in [400, 500]

@patch('utils.recaptcha.verify_recaptcha')
def test_login_with_recaptcha_success(mock_verify_recaptcha):
    """Test login endpoint with successful reCAPTCHA verification"""
    # Mock successful reCAPTCHA verification
    mock_verify_recaptcha.return_value = AsyncMock(return_value=True)
    
    # Mock database operations
    with patch('routes.auth.get_session') as mock_session:
        mock_db_session = AsyncMock()
        mock_session.return_value = mock_db_session
        
        # Mock user authentication (simulate successful login)
        from models.user import User
        mock_user = User(
            id=1,
            email="<EMAIL>",
            hashed_password="$2b$12$test_hash",  # Mock hashed password
            username="testuser_recaptcha",
            name="Test User",
            country="Test Country",
            phone_no="1234567890",
            address="Test Address"
        )
        mock_db_session.exec.return_value.first.return_value = mock_user
        
        # Mock password verification
        with patch('routes.auth.verify_password', return_value=True):
            # Mock verification status
            mock_db_session.exec.return_value.first.return_value = None  # No verification record
            
            # Mock JWT token creation
            with patch('routes.auth.create_access_token', return_value="mock_jwt_token"):
                response = client.post(
                    "/auth/login",
                    data=test_login_data,
                    params={"recaptcha_token": "test_recaptcha_token_123"}
                )
                
                # Verify reCAPTCHA was called
                mock_verify_recaptcha.assert_called_once_with("test_recaptcha_token_123", None)
                
                # Check response
                assert response.status_code in [200, 400, 500]

@patch('utils.recaptcha.verify_recaptcha')
def test_login_with_recaptcha_failure(mock_verify_recaptcha):
    """Test login endpoint with failed reCAPTCHA verification"""
    # Mock failed reCAPTCHA verification
    mock_verify_recaptcha.side_effect = Exception("reCAPTCHA verification failed")
    
    response = client.post(
        "/auth/login",
        data=test_login_data,
        params={"recaptcha_token": "test_recaptcha_token_123"}
    )
    
    # Should return an error when reCAPTCHA fails
    assert response.status_code in [400, 500]

@patch('utils.recaptcha.verify_recaptcha')
def test_forgot_password_with_recaptcha_success(mock_verify_recaptcha):
    """Test forgot password endpoint with successful reCAPTCHA verification"""
    # Mock successful reCAPTCHA verification
    mock_verify_recaptcha.return_value = AsyncMock(return_value=True)
    
    # Mock database operations
    with patch('routes.auth.get_session') as mock_session:
        mock_db_session = AsyncMock()
        mock_session.return_value = mock_db_session
        
        # Mock user existence (user exists)
        from models.user import User
        mock_user = User(
            id=1,
            email="<EMAIL>",
            hashed_password="$2b$12$test_hash",
            username="testuser_recaptcha",
            name="Test User",
            country="Test Country",
            phone_no="1234567890",
            address="Test Address"
        )
        mock_db_session.exec.return_value.first.return_value = mock_user
        
        # Mock token creation
        mock_db_session.add.return_value = None
        mock_db_session.commit.return_value = None
        
        # Mock email sending
        with patch('routes.auth.send_password_reset_email'):
            response = client.post("/auth/forgot-password", json=test_forgot_password_data)
            
            # Verify reCAPTCHA was called
            mock_verify_recaptcha.assert_called_once_with("test_recaptcha_token_456", None)
            
            # Check response
            assert response.status_code == 200
            assert "message" in response.json()

@patch('utils.recaptcha.verify_recaptcha')
def test_forgot_password_with_recaptcha_failure(mock_verify_recaptcha):
    """Test forgot password endpoint with failed reCAPTCHA verification"""
    # Mock failed reCAPTCHA verification
    mock_verify_recaptcha.side_effect = Exception("reCAPTCHA verification failed")
    
    response = client.post("/auth/forgot-password", json=test_forgot_password_data)
    
    # Should return an error when reCAPTCHA fails
    assert response.status_code in [400, 500]

def test_missing_recaptcha_token_signup():
    """Test signup without reCAPTCHA token"""
    data_without_token = test_signup_data.copy()
    del data_without_token["recaptcha_token"]
    
    response = client.post("/auth/signup", json=data_without_token)
    
    # Should return validation error for missing token
    assert response.status_code == 422

def test_missing_recaptcha_token_login():
    """Test login without reCAPTCHA token"""
    response = client.post("/auth/login", data=test_login_data)
    
    # Should return validation error for missing token
    assert response.status_code == 422

def test_missing_recaptcha_token_forgot_password():
    """Test forgot password without reCAPTCHA token"""
    data_without_token = test_forgot_password_data.copy()
    del data_without_token["recaptcha_token"]
    
    response = client.post("/auth/forgot-password", json=data_without_token)
    
    # Should return validation error for missing token
    assert response.status_code == 422

@patch('utils.recaptcha.verify_recaptcha')
def test_recaptcha_score_too_low(mock_verify_recaptcha):
    """Test behavior when reCAPTCHA score is too low"""
    from fastapi import HTTPException
    
    # Mock reCAPTCHA score too low
    mock_verify_recaptcha.side_effect = HTTPException(
        status_code=400,
        detail="reCAPTCHA score too low: 0.3 (minimum: 0.5)"
    )
    
    response = client.post("/auth/signup", json=test_signup_data)
    
    # Should return 400 error for low score
    assert response.status_code == 400
    assert "score too low" in response.json()["detail"]

def test_endpoint_urls_exist():
    """Test that all required endpoints exist"""
    # Test signup endpoint
    response = client.post("/auth/signup", json=test_signup_data)
    # Should not be 404 (endpoint exists)
    assert response.status_code != 404
    
    # Test login endpoint
    response = client.post("/auth/login", data=test_login_data, params={"recaptcha_token": "test"})
    # Should not be 404 (endpoint exists)
    assert response.status_code != 404
    
    # Test forgot password endpoint
    response = client.post("/auth/forgot-password", json=test_forgot_password_data)
    # Should not be 404 (endpoint exists)
    assert response.status_code != 404

if __name__ == "__main__":
    print("Running reCAPTCHA integration tests...")
    print("These tests verify that reCAPTCHA is properly integrated into:")
    print("1. /auth/signup")
    print("2. /auth/login") 
    print("3. /auth/forgot-password")
    print("\nRun with: pytest test_recaptcha_integration.py -v") 