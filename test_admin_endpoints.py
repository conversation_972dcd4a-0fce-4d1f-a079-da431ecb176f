import requests
import json

# Base URL for the API
BASE_URL = "http://localhost:8000"

def test_admin_endpoints():
    print("Testing Admin Endpoints")
    print("=" * 50)
    
    # Test 1: Admin Signup
    print("\n1. Testing /admin/signup")
    signup_data = {
        "username": "admin_test",
        "email": "<EMAIL>",
        "password": "Admin123!",
        "name": "Test Admin",
        "role": "admin"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/admin/signup", json=signup_data)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✅ Admin signup successful")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Admin signup failed: {response.text}")
    except Exception as e:
        print(f"❌ Error in admin signup: {str(e)}")
    
    # Test 2: Admin Login (without 2FA)
    print("\n2. Testing /admin/login")
    login_data = {
        "username": "admin_test",
        "password": "Admin123!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/admin/login", json=login_data)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✅ Admin login successful")
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            
            # Store access token for further tests
            if "access_token" in result:
                access_token = result["access_token"]
                print(f"Access Token: {access_token[:50]}...")
                
                # Test 3: Get current admin profile
                print("\n3. Testing /admin/me")
                headers = {"Authorization": f"Bearer {access_token}"}
                profile_response = requests.get(f"{BASE_URL}/admin/me", headers=headers)
                print(f"Status Code: {profile_response.status_code}")
                if profile_response.status_code == 200:
                    print("✅ Get admin profile successful")
                    print(f"Profile: {json.dumps(profile_response.json(), indent=2)}")
                else:
                    print(f"❌ Get admin profile failed: {profile_response.text}")
                    
        else:
            print(f"❌ Admin login failed: {response.text}")
    except Exception as e:
        print(f"❌ Error in admin login: {str(e)}")
    
    # Test 4: Setup 2FA
    print("\n4. Testing /admin/setup-2fa")
    try:
        if 'access_token' in locals():
            headers = {"Authorization": f"Bearer {access_token}"}
            setup_response = requests.post(f"{BASE_URL}/admin/setup-2fa", headers=headers)
            print(f"Status Code: {setup_response.status_code}")
            if setup_response.status_code == 200:
                print("✅ 2FA setup successful")
                setup_result = setup_response.json()
                print(f"Secret Key: {setup_result['secret_key']}")
                print(f"QR Code URL: {setup_result['qr_code_url'][:100]}...")
            else:
                print(f"❌ 2FA setup failed: {setup_response.text}")
        else:
            print("❌ Skipping 2FA setup - no access token available")
    except Exception as e:
        print(f"❌ Error in 2FA setup: {str(e)}")

if __name__ == "__main__":
    test_admin_endpoints() 