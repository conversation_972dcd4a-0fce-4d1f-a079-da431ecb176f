from jose import <PERSON><PERSON><PERSON>rro<PERSON>, jwt
from passlib.context import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from datetime import datetime, timedelta
import config
from db import get_session
from models.admin import Admin, Admin2FAToken
from sqlmodel import Session, select
from fastapi import HTTPException, Depends
from fastapi.security import <PERSON>Auth2PasswordBearer
import secrets
import pyotp
import qrcode
import base64
from io import BytesIO

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2PasswordBearer for admin tokens
admin_oauth2_scheme = OAuth2PasswordBearer(tokenUrl="admin/login")

# Create a password hash
def get_admin_password_hash(password: str) -> str:
    return pwd_context.hash(password)

# Verify the password
def verify_admin_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

# Create a new JWT token for admin
def create_admin_access_token(data: dict) -> str:
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "type": "admin"})  # Mark as admin token
    encoded_jwt = jwt.encode(to_encode, config.JWT_SECRET_KEY, algorithm=config.ALGORITHM)
    return encoded_jwt

# Decode and verify JWT token for admin
def decode_admin_token(token: str) -> str:
    try:
        payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.ALGORITHM])
        admin_id: str = payload.get("sub")
        token_type: str = payload.get("type")
        
        if admin_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        if token_type != "admin":
            raise HTTPException(status_code=401, detail="Invalid token type")
            
        return admin_id
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Get the current logged-in admin
def get_current_admin(token: str = Depends(admin_oauth2_scheme), session: Session = Depends(get_session)) -> Admin:
    admin_id = decode_admin_token(token)
    admin = session.exec(select(Admin).where(Admin.id == admin_id)).first()
    if admin is None:
        raise HTTPException(status_code=401, detail="Admin not found")
    if not admin.is_active:
        raise HTTPException(status_code=401, detail="Admin account is inactive")
    return admin

# Generate 2FA secret
def generate_2fa_secret() -> str:
    return pyotp.random_base32()

# Generate QR code for 2FA setup
def generate_2fa_qr_code(secret: str, username: str, issuer: str = "Funded Horizon") -> str:
    totp = pyotp.TOTP(secret)
    provisioning_uri = totp.provisioning_uri(name=username, issuer_name=issuer)
    
    # Generate QR code
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(provisioning_uri)
    qr.make(fit=True)
    
    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return f"data:image/png;base64,{img_str}"

# Verify 2FA code
def verify_2fa_code(secret: str, code: str) -> bool:
    totp = pyotp.TOTP(secret)
    return totp.verify(code)

# Generate 2FA token for verification
def generate_2fa_token() -> str:
    return secrets.token_urlsafe(32)

# Create 2FA token record
def create_2fa_token_record(admin_id: int, session: Session) -> str:
    token = generate_2fa_token()
    token_record = Admin2FAToken(
        admin_id=admin_id,
        token=token,
        expires_at=datetime.utcnow() + timedelta(minutes=5)  # 5 minutes expiry
    )
    session.add(token_record)
    session.commit()
    return token 