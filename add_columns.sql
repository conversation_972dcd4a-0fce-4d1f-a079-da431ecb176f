-- Add created_at column to ordermodel table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='ordermodel' AND column_name='created_at'
    ) THEN
        ALTER TABLE ordermodel 
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column created_at added to ordermodel table';
    ELSE
        RAISE NOTICE 'Column created_at already exists in ordermodel table';
    END IF;
END $$;

-- Add completed_at column to completeordermodel table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='completeordermodel' AND column_name='completed_at'
    ) THEN
        ALTER TABLE completeordermodel 
        ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column completed_at added to completeordermodel table';
    ELSE
        RAISE NOTICE 'Column completed_at already exists in completeordermodel table';
    END IF;
END $$;

-- Add rejected_at column to rejectorder table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='rejectorder' AND column_name='rejected_at'
    ) THEN
        ALTER TABLE rejectorder 
        ADD COLUMN rejected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column rejected_at added to rejectorder table';
    ELSE
        RAISE NOTICE 'Column rejected_at already exists in rejectorder table';
    END IF;
END $$;

-- Add pass_date column to passorder table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='passorder' AND column_name='pass_date'
    ) THEN
        ALTER TABLE passorder 
        ADD COLUMN pass_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column pass_date added to passorder table';
    ELSE
        RAISE NOTICE 'Column pass_date already exists in passorder table';
    END IF;
END $$;

-- Add created_at column to stage2account table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='stage2account' AND column_name='created_at'
    ) THEN
        ALTER TABLE stage2account 
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column created_at added to stage2account table';
    ELSE
        RAISE NOTICE 'Column created_at already exists in stage2account table';
    END IF;
END $$;

-- Add created_at column to liveaccount table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='liveaccount' AND column_name='created_at'
    ) THEN
        ALTER TABLE liveaccount 
        ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column created_at added to liveaccount table';
    ELSE
        RAISE NOTICE 'Column created_at already exists in liveaccount table';
    END IF;
END $$;

-- Add event_date column to ordertimeline table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name='ordertimeline' AND column_name='event_date'
    ) THEN
        ALTER TABLE ordertimeline 
        ADD COLUMN event_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Column event_date added to ordertimeline table';
    ELSE
        RAISE NOTICE 'Column event_date already exists in ordertimeline table';
    END IF;
END $$;

-- Create failorder_failedat table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables WHERE table_name='failorder_failedat'
    ) THEN
        CREATE TABLE failorder_failedat (
            id SERIAL PRIMARY KEY,
            failorder_id INTEGER REFERENCES failorder(id) ON DELETE CASCADE,
            failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        RAISE NOTICE 'Table failorder_failedat created';
    ELSE
        RAISE NOTICE 'Table failorder_failedat already exists';
    END IF;
END $$;

-- Remove failed_at column from failorder if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns WHERE table_name='failorder' AND column_name='failed_at'
    ) THEN
        ALTER TABLE failorder DROP COLUMN failed_at;
        RAISE NOTICE 'Column failed_at dropped from failorder table';
    ELSE
        RAISE NOTICE 'Column failed_at does not exist in failorder table';
    END IF;
END $$;
