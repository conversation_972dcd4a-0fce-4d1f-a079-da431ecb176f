from sqlmodel import Session, select
from models.user import User
from models.user_referral import UserReferral
from db import get_session
from config import DATABASE_URL
import psycopg2

def test_new_referral_structure():
    """Test the new referral structure"""
    try:
        print("🧪 Testing new referral structure...")
        
        # Connect to database
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Check if user_referral table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'user_referral'
            );
        """)
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            print("❌ user_referral table does not exist!")
            return False
        
        print("✅ user_referral table exists")
        
        # Count records in both tables
        cursor.execute("SELECT COUNT(*) FROM \"user\"")
        user_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user_referral")
        referral_count = cursor.fetchone()[0]
        
        print(f"📊 Users: {user_count}, Referral records: {referral_count}")
        
        # Check a few sample records
        cursor.execute("""
            SELECT u.id, u.username, u.email, ur.referral_code, ur.total_points
            FROM "user" u
            LEFT JOIN user_referral ur ON u.id = ur.user_id
            LIMIT 5
        """)
        
        sample_records = cursor.fetchall()
        print(f"\n📋 Sample records:")
        print("-" * 80)
        for record in sample_records:
            user_id, username, email, referral_code, points = record
            print(f"User ID: {user_id}, Username: {username}, Email: {email}")
            print(f"Referral Code: {referral_code}, Points: {points}")
            print("-" * 40)
        
        # Test referral code lookup
        if sample_records:
            test_referral_code = sample_records[0][3]  # Get first referral code
            if test_referral_code:
                cursor.execute("""
                    SELECT ur.referral_code, u.username, u.email
                    FROM user_referral ur
                    JOIN "user" u ON ur.user_id = u.id
                    WHERE ur.referral_code = %s
                """, (test_referral_code,))
                
                result = cursor.fetchone()
                if result:
                    print(f"\n✅ Referral code lookup works: {result[0]} -> {result[1]} ({result[2]})")
                else:
                    print(f"\n❌ Referral code lookup failed for: {test_referral_code}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 New referral structure test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing referral structure: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_new_referral_structure() 