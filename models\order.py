from sqlmodel import SQLMode<PERSON>, Field, Relationship
from typing import Optional, List
from datetime import datetime
from enum import Enum
from models.rejected_at import RejectedAt

class OrderImage(SQLModel, table=True):
    __tablename__ = "orderimage"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    image_url: str
    cloudinary_public_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationship
    order: "OrderModel" = Relationship(back_populates="images")

class OrderModel(SQLModel, table=True):
    __tablename__ = "ordermodel"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    username: str
    email: str
    challenge_type: str
    account_size: str
    platform: str
    payment_method: str
    txid: str
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow)

    # Relationship
    images: List[OrderImage] = Relationship(back_populates="order")

class DateTimeModel(SQLModel, table=True):
    __tablename__ = "datetimemodel"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

class PassOrder(SQLModel, table=True):
    __tablename__ = "passorder"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    pass_date: datetime = Field(default_factory=datetime.utcnow)
    profit_amount: Optional[float] = None
    notes: Optional[str] = None

class OrderTimeline(SQLModel, table=True):
    __tablename__ = "ordertimeline"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    event_type: str  # "created", "completed", "passed", "failed", "stage2_created", "live_created"
    event_date: datetime = Field(default_factory=datetime.utcnow)
    notes: Optional[str] = None

class CompleteOrderModel(SQLModel, table=True):
    __tablename__ = "completeordermodel"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    server: str
    platform_login: str
    platform_password: str
    session_id: Optional[str] = None
    terminal_id: Optional[int] = None
    profit_target: Optional[int] = None
    completed_at: datetime = Field(default_factory=datetime.utcnow)

class LiveAccount(SQLModel, table=True):
    __tablename__ = "liveaccount"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    server: str
    platform_login: str
    platform_password: str
    session_id: Optional[str] = None
    terminal_id: Optional[int] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    profit_share: float = 80.0  # Default profit share percentage
    status: str = "active"  # active, inactive

class Stage2Account(SQLModel, table=True):
    __tablename__ = "stage2account"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    server: str
    platform_login: str
    platform_password: str
    session_id: Optional[str] = None
    terminal_id: Optional[int] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    profit_target: Optional[float] = None
    status: str = "active"  # active, completed, failed

class OrderStatus(SQLModel, table=True):
    __tablename__ = "orderstatus"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    is_active: bool
    status: str  # "pass" or "fail"

class RejectOrder(SQLModel, table=True):
    __tablename__ = "rejectorder"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    reason: str
    rejected_at_entry: Optional["RejectedAt"] = Relationship(back_populates="reject_order")

class FailOrder(SQLModel, table=True):
    __tablename__ = "failorder"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    reason: str
    failed_at_entry: Optional["FailOrderFailedAt"] = Relationship(back_populates="fail_order")

class FailOrderFailedAt(SQLModel, table=True):
    __tablename__ = "failorder_failedat"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    failorder_id: int = Field(foreign_key="failorder.id")
    failed_at: datetime = Field(default_factory=datetime.utcnow)
    fail_order: Optional[FailOrder] = Relationship(back_populates="failed_at_entry")

class Certificate(SQLModel, table=True):
    __tablename__ = "certificate"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    order_id: int = Field(foreign_key="ordermodel.id")
    certificate_number: str
    issue_date: datetime = Field(default_factory=datetime.utcnow)
    account_size: str
    challenge_type: str
    profit_target: Optional[float] = None

class FailureReason(str, Enum):
    DAILY_DRAWDOWN_5 = "Policy Violation Detection - 5% Daily Drawdown Exceeded"
    OVERALL_DRAWDOWN = "Breach Alert - Overall Drawdown Limit Violated"
    PHASE1_TRADING_DAYS = "Trading Breach - Phase-1 Minimum Trading Days Not Met"
    DAILY_DRAWDOWN_4 = "Policy Violation Detection - 4% Daily Drawdown Exceeded"
    PHASE2_TRADING_DAYS = "Trading Breach - Phase-2 Minimum Trading Days Requirement Unfulfilled"

class AccountType(str, Enum):
    STAGE2 = "stage2"
    LIVE = "live"

RejectOrder.update_forward_refs()
RejectedAt.update_forward_refs()
