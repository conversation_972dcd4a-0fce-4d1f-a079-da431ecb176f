-- Comprehensive SQL script to add all missing columns to database tables
-- Run this script on your PostgreSQL database

-- 1. User table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user' AND column_name = 'referred_by'
    ) THEN
        ALTER TABLE "user" ADD COLUMN referred_by VARCHAR;
        RAISE NOTICE 'Added referred_by column to user table';
    ELSE
        RAISE NOTICE 'referred_by column already exists in user table';
    END IF;
END $$;

-- 2. OrderModel table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'ordermodel' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE ordermodel ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to ordermodel table';
    ELSE
        RAISE NOTICE 'created_at column already exists in ordermodel table';
    END IF;
END $$;

-- 3. CompleteOrderModel table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'completeordermodel' AND column_name = 'completed_at'
    ) THEN
        ALTER TABLE completeordermodel ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added completed_at column to completeordermodel table';
    ELSE
        RAISE NOTICE 'completed_at column already exists in completeordermodel table';
    END IF;
END $$;

-- 4. FailOrder table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'failorder' AND column_name = 'failed_at'
    ) THEN
        ALTER TABLE failorder ADD COLUMN failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added failed_at column to failorder table';
    ELSE
        RAISE NOTICE 'failed_at column already exists in failorder table';
    END IF;
END $$;

-- 5. RejectOrder table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'rejectorder' AND column_name = 'rejected_at'
    ) THEN
        ALTER TABLE rejectorder ADD COLUMN rejected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added rejected_at column to rejectorder table';
    ELSE
        RAISE NOTICE 'rejected_at column already exists in rejectorder table';
    END IF;
END $$;

-- 6. PassOrder table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'passorder' AND column_name = 'pass_date'
    ) THEN
        ALTER TABLE passorder ADD COLUMN pass_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added pass_date column to passorder table';
    ELSE
        RAISE NOTICE 'pass_date column already exists in passorder table';
    END IF;
END $$;

-- 7. OrderTimeline table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'ordertimeline' AND column_name = 'event_date'
    ) THEN
        ALTER TABLE ordertimeline ADD COLUMN event_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added event_date column to ordertimeline table';
    ELSE
        RAISE NOTICE 'event_date column already exists in ordertimeline table';
    END IF;
END $$;

-- 8. OrderImage table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'orderimage' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE orderimage ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to orderimage table';
    ELSE
        RAISE NOTICE 'created_at column already exists in orderimage table';
    END IF;
END $$;

-- 9. LiveAccount table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'liveaccount' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to liveaccount table';
    ELSE
        RAISE NOTICE 'created_at column already exists in liveaccount table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'liveaccount' AND column_name = 'profit_share'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN profit_share FLOAT DEFAULT 80.0;
        RAISE NOTICE 'Added profit_share column to liveaccount table';
    ELSE
        RAISE NOTICE 'profit_share column already exists in liveaccount table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'liveaccount' AND column_name = 'status'
    ) THEN
        ALTER TABLE liveaccount ADD COLUMN status VARCHAR DEFAULT 'active';
        RAISE NOTICE 'Added status column to liveaccount table';
    ELSE
        RAISE NOTICE 'status column already exists in liveaccount table';
    END IF;
END $$;

-- 10. Stage2Account table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'stage2account' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added created_at column to stage2account table';
    ELSE
        RAISE NOTICE 'created_at column already exists in stage2account table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'stage2account' AND column_name = 'profit_target'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN profit_target FLOAT;
        RAISE NOTICE 'Added profit_target column to stage2account table';
    ELSE
        RAISE NOTICE 'profit_target column already exists in stage2account table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'stage2account' AND column_name = 'status'
    ) THEN
        ALTER TABLE stage2account ADD COLUMN status VARCHAR DEFAULT 'active';
        RAISE NOTICE 'Added status column to stage2account table';
    ELSE
        RAISE NOTICE 'status column already exists in stage2account table';
    END IF;
END $$;

-- 11. Certificate table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'certificate' AND column_name = 'issue_date'
    ) THEN
        ALTER TABLE certificate ADD COLUMN issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added issue_date column to certificate table';
    ELSE
        RAISE NOTICE 'issue_date column already exists in certificate table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'certificate' AND column_name = 'profit_target'
    ) THEN
        ALTER TABLE certificate ADD COLUMN profit_target FLOAT;
        RAISE NOTICE 'Added profit_target column to certificate table';
    ELSE
        RAISE NOTICE 'profit_target column already exists in certificate table';
    END IF;
END $$;

-- 12. DateTimeModel table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'datetimemodel' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE datetimemodel ADD COLUMN updated_at TIMESTAMP;
        RAISE NOTICE 'Added updated_at column to datetimemodel table';
    ELSE
        RAISE NOTICE 'updated_at column already exists in datetimemodel table';
    END IF;
END $$;

-- Verify all columns were added
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN (
    'user', 'ordermodel', 'completeordermodel', 'failorder', 
    'rejectorder', 'passorder', 'ordertimeline', 'orderimage',
    'liveaccount', 'stage2account', 'certificate', 'datetimemodel'
)
ORDER BY table_name, column_name; 