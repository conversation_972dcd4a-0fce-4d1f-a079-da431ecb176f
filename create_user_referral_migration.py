import psycopg2
from config import DATABASE_URL
import sys

def create_user_referral_table():
    """Create the user_referral table and migrate existing data"""
    try:
        print("🔄 Starting user referral table migration...")
        
        # Connect to database
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Read and execute the SQL migration
        with open('create_user_referral_table.sql', 'r') as file:
            sql_script = file.read()
        
        print("📝 Executing SQL migration...")
        cursor.execute(sql_script)
        
        # Commit the changes
        conn.commit()
        
        # Verify the table was created
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'user_referral'
            );
        """)
        table_exists = cursor.fetchone()[0]
        
        if table_exists:
            print("✅ User referral table created successfully!")
            
            # Check table structure
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = 'user_referral'
                ORDER BY ordinal_position;
            """)
            
            columns = cursor.fetchall()
            print(f"\n📋 User referral table structure:")
            print("-" * 80)
            for col in columns:
                print(f"Column: {col[0]:<20} Type: {col[1]:<15} Nullable: {col[2]:<5} Default: {col[3]}")
            
            # Count records
            cursor.execute("SELECT COUNT(*) FROM user_referral")
            referral_count = cursor.fetchone()[0]
            print(f"\n📊 Total referral records: {referral_count}")
            
        else:
            print("❌ Failed to create user referral table!")
            return False
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_user_referral_table()
    if not success:
        sys.exit(1) 