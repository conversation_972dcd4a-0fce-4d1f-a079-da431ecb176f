import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from config import R<PERSON><PERSON><PERSON><PERSON>_SECRET_KEY, RECAPTCHA_VERIFY_URL

def verify_recaptcha(token: str, remote_ip: str = None) -> bool:
    """
    Verify reCAPTCHA v2 token with Google's API
    Args:
        token: The reCAPTCHA token from the client
        remote_ip: The IP address of the client (optional)
    Returns:
        bool: True if verification passes, False otherwise
    """
    if not RECAPTCHA_SECRET_KEY:
        # If no secret key is configured, skip verification (for development)
        return True
    if not token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="reCAPTCHA token is required"
        )
    data = {
        'secret': RECAPTCHA_SECRET_KEY,
        'response': token
    }
    if remote_ip:
        data['remoteip'] = remote_ip
    try:
        response = requests.post(RECAPTCHA_VERIFY_URL, data=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        if not result.get('success', False):
            error_codes = result.get('error-codes', [])
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"reCAPTCHA verification failed: {', '.join(error_codes)}"
            )
        return True
    except requests.RequestException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to verify reCAPTCHA: {str(e)}"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error during reCAPTCHA verification: {str(e)}"
        ) 