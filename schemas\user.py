from sqlmodel import SQLModel
from pydantic import EmailStr
from pydantic import ConfigDict  # Import for Pydantic v2 configuration
from typing import Optional
from datetime import datetime

class UserCreate(SQLModel):
    username: str
    email: EmailStr
    password: str
    name: str
    phone_no: str
    country: str
    address: str
    referral_code: Optional[str] = None  # Optional referral code when signing up

class UserCreateWithRecaptcha(UserCreate):
    recaptcha_token: str

class UserLogin(SQLModel):
    email: EmailStr
    password: str

class UserLoginWithRecaptcha(UserLogin):
    recaptcha_token: str

class ForgotPasswordRequest(SQLModel):
    email: EmailStr
    recaptcha_token: str

class UserReferralResponse(SQLModel):
    id: int
    user_id: int
    referral_code: str
    total_points: float
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class UserResponse(SQLModel):
    id: int
    username: str
    email: EmailStr
    name: str
    country: str
    phone_no: str
    address: str
    created_at: datetime
    referred_by: Optional[str] = None
    user_referral: Optional[UserReferralResponse] = None

    model_config = ConfigDict(from_attributes=True)  # Pydantic v2 compatibility for orm_mode

class UserWithVerification(UserResponse):
    is_verified: bool = False

    model_config = ConfigDict(from_attributes=True)

class Token(SQLModel):
    access_token: str
    token_type: str
    is_verified: bool = False
