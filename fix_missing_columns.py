#!/usr/bin/env python3
"""
Script to add missing columns to database tables
"""

import os
import sys
from sqlmodel import SQLModel, create_engine, Session, text
from models.user import User
from models.order import CompleteOrderModel
import sqlalchemy
from sqlalchemy import create_engine, text

# Database URL - update this with your actual database URL
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://username:password@localhost/dbname")

def add_missing_columns():
    """Add missing columns to database tables"""
    
    # Create engine
    engine = create_engine(DATABASE_URL)
    
    with Session(engine) as session:
        try:
            # Check if referred_by column exists in user table
            result = session.exec(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'user' AND column_name = 'referred_by'
            """)).first()
            
            if not result:
                print("Adding 'referred_by' column to user table...")
                session.exec(text("ALTER TABLE \"user\" ADD COLUMN referred_by VARCHAR"))
                session.commit()
                print("✅ Added 'referred_by' column to user table")
            else:
                print("✅ 'referred_by' column already exists in user table")
            
            # Check if completed_at column exists in completeordermodel table
            result = session.exec(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'completeordermodel' AND column_name = 'completed_at'
            """)).first()
            
            if not result:
                print("Adding 'completed_at' column to completeordermodel table...")
                session.exec(text("ALTER TABLE completeordermodel ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"))
                session.commit()
                print("✅ Added 'completed_at' column to completeordermodel table")
            else:
                print("✅ 'completed_at' column already exists in completeordermodel table")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            session.rollback()
            raise

def verify_columns():
    """Verify that all required columns exist"""
    
    engine = create_engine(DATABASE_URL)
    
    with Session(engine) as session:
        try:
            # Check user table columns
            result = session.exec(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'user'
                ORDER BY column_name
            """)).all()
            
            print("User table columns:")
            for row in result:
                print(f"  - {row[0]}")
            
            # Check completeordermodel table columns
            result = session.exec(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'completeordermodel'
                ORDER BY column_name
            """)).all()
            
            print("\nCompleteOrderModel table columns:")
            for row in result:
                print(f"  - {row[0]}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            raise

def add_failed_at_column():
    engine = create_engine(DATABASE_URL)
    with engine.connect() as conn:
        # Check if the column already exists
        result = conn.execute(text("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name='failorder' AND column_name='failed_at';
        """))
        if not result.fetchone():
            print("Adding 'failed_at' column to 'failorder' table...")
            conn.execute(text("ALTER TABLE failorder ADD COLUMN failed_at TIMESTAMP;"))
            print("Column added.")
        else:
            print("'failed_at' column already exists.")

if __name__ == "__main__":
    print("🔧 Database Column Migration Script")
    print("=" * 40)
    
    try:
        add_missing_columns()
        print("\n" + "=" * 40)
        verify_columns()
        print("\n✅ Migration completed successfully!")
        add_failed_at_column()
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1) 