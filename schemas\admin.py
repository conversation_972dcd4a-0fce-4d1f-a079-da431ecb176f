from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class AdminCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    name: str
    role: str = "admin"

class AdminLogin(BaseModel):
    username: str
    password: str

class Admin2FAVerify(BaseModel):
    token: str
    two_factor_code: str

class AdminResponse(BaseModel):
    id: int
    username: str
    email: EmailStr
    name: str
    role: str
    is_active: bool
    two_factor_enabled: bool
    last_login: Optional[datetime]
    created_at: datetime

class AdminToken(BaseModel):
    access_token: str
    token_type: str
    admin: AdminResponse

class Admin2FASetup(BaseModel):
    secret_key: str
    qr_code_url: str 