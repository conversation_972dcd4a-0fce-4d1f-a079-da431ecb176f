#!/usr/bin/env python3
"""
Verification script to test reCAPTCHA integration
"""

import sys
import os

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from utils.recaptcha import verify_recaptcha
        print("✓ utils.recaptcha imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import utils.recaptcha: {e}")
        return False
    
    try:
        from schemas.user import UserCreateWithRecaptcha, UserLoginWithRecaptcha, ForgotPasswordRequest
        print("✓ reCAPTCHA schemas imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import reCAPTCHA schemas: {e}")
        return False
    
    try:
        from routes.auth import auth_router
        print("✓ auth_router imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import auth_router: {e}")
        return False
    
    try:
        from config import RECAPTCHA_SECRET_KEY, RECAPTCHA_SITE_KEY, RECAPTCHA_MIN_SCORE
        print("✓ reCAPTCHA config imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import reCAPTCHA config: {e}")
        return False
    
    return True

def test_config():
    """Test reCAPTCHA configuration"""
    print("\nTesting reCAPTCHA configuration...")
    
    from config import RECAPTCHA_SECRET_KEY, RECAPTCHA_SITE_KEY, RECAPTCHA_MIN_SCORE
    
    print(f"RECAPTCHA_SECRET_KEY: {'✓ Set' if RECAPTCHA_SECRET_KEY else '⚠ Not set (will skip verification)'}")
    print(f"RECAPTCHA_SITE_KEY: {'✓ Set' if RECAPTCHA_SITE_KEY else '⚠ Not set'}")
    print(f"RECAPTCHA_MIN_SCORE: {RECAPTCHA_MIN_SCORE}")
    
    return True

def test_schemas():
    """Test that schemas work correctly"""
    print("\nTesting reCAPTCHA schemas...")
    
    from schemas.user import UserCreateWithRecaptcha, UserLoginWithRecaptcha, ForgotPasswordRequest
    
    # Test signup schema
    try:
        signup_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpass123",
            "name": "Test User",
            "phone_no": "1234567890",
            "country": "Test Country",
            "address": "Test Address",
            "recaptcha_token": "test_token"
        }
        signup_schema = UserCreateWithRecaptcha(**signup_data)
        print("✓ UserCreateWithRecaptcha schema works")
    except Exception as e:
        print(f"✗ UserCreateWithRecaptcha schema failed: {e}")
        return False
    
    # Test login schema
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "testpass123",
            "recaptcha_token": "test_token"
        }
        login_schema = UserLoginWithRecaptcha(**login_data)
        print("✓ UserLoginWithRecaptcha schema works")
    except Exception as e:
        print(f"✗ UserLoginWithRecaptcha schema failed: {e}")
        return False
    
    # Test forgot password schema
    try:
        forgot_data = {
            "email": "<EMAIL>",
            "recaptcha_token": "test_token"
        }
        forgot_schema = ForgotPasswordRequest(**forgot_data)
        print("✓ ForgotPasswordRequest schema works")
    except Exception as e:
        print(f"✗ ForgotPasswordRequest schema failed: {e}")
        return False
    
    return True

def test_recaptcha_function():
    """Test reCAPTCHA verification function"""
    print("\nTesting reCAPTCHA verification function...")
    
    from utils.recaptcha import verify_recaptcha
    from config import RECAPTCHA_SECRET_KEY
    import asyncio
    
    async def test_verify():
        try:
            if not RECAPTCHA_SECRET_KEY:
                # Test with empty secret key (should skip verification)
                result = await verify_recaptcha("test_token")
                print("✓ reCAPTCHA verification function works (skipped due to no secret key)")
                return True
            else:
                # Test with real secret key (will fail with test token, which is expected)
                try:
                    result = await verify_recaptcha("test_token")
                    print("✓ reCAPTCHA verification function works")
                    return True
                except Exception as e:
                    if "invalid-input-response" in str(e) or "score too low" in str(e):
                        print("✓ reCAPTCHA verification function works (correctly rejected test token)")
                        return True
                    else:
                        print(f"✗ reCAPTCHA verification function failed: {e}")
                        return False
        except Exception as e:
            print(f"✗ reCAPTCHA verification function failed: {e}")
            return False
    
    return asyncio.run(test_verify())

def test_endpoints():
    """Test that endpoints are properly configured"""
    print("\nTesting endpoint configuration...")
    
    from routes.auth import auth_router
    
    # Check if endpoints exist
    endpoints = [route.path for route in auth_router.routes]
    
    required_endpoints = [
        "/auth/signup",
        "/auth/login", 
        "/auth/forgot-password"
    ]
    
    for endpoint in required_endpoints:
        if endpoint in endpoints:
            print(f"✓ {endpoint} endpoint exists")
        else:
            print(f"✗ {endpoint} endpoint missing")
            return False
    
    return True

def main():
    """Main verification function"""
    print("=" * 60)
    print("reCAPTCHA Integration Verification")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Schemas", test_schemas),
        ("reCAPTCHA Function", test_recaptcha_function),
        ("Endpoints", test_endpoints)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} test failed")
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Verification Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! reCAPTCHA integration is working correctly.")
        print("\nNext steps:")
        print("1. Set RECAPTCHA_SECRET_KEY and RECAPTCHA_SITE_KEY in your .env file")
        print("2. Test the endpoints with real reCAPTCHA tokens")
        print("3. Run the integration tests: pytest test_recaptcha_integration.py -v")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 