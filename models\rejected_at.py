from sqlmodel import SQLModel, Field, Relationship
from typing import Optional
from datetime import datetime

class RejectedAt(SQLModel, table=True):
    __tablename__ = "rejected_at"
    __table_args__ = {'extend_existing': True}

    id: Optional[int] = Field(default=None, primary_key=True)
    rejectorder_id: int = Field(foreign_key="rejectorder.id")
    rejected_at: datetime = Field(default_factory=datetime.utcnow)
    reject_order: Optional["RejectOrder"] = Relationship(back_populates="rejected_at_entry")

RejectedAt.update_forward_refs() 