import os
import sys
import requests
from dotenv import load_dotenv
import random

# Load environment variables
load_dotenv()

# Get API base URL from environment variables or use default
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

def test_assign_account_with_type():
    """
    Test assigning an account to an order with different assignment types
    
    This test:
    1. Creates a new account credential
    2. Creates a new order
    3. Assigns the account to the order with different assignment types
    4. Verifies the assignment was successful
    """
    print("Starting test: Assign account with different assignment types")
    
    # Test each assignment type
    assignment_types = ["complete", "stage2", "live"]
    
    for assignment_type in assignment_types:
        print(f"\n\n=== Testing assignment type: {assignment_type.upper()} ===")
        
        # Step 1: Create a new account credential
        print("\nStep 1: Creating a new account credential...")
        credentials_data = {
            "credentials": [
                {
                    "platform": "MT4",
                    "server": "Test Server",
                    "platform_login": f"test_login_{random.randint(10000, 99999)}",
                    "platform_password": "test_password",
                    "account_size": "10K",
                    "account_type": "phase1"
                }
            ]
        }
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/account/credentials",
                json=credentials_data
            )
            response.raise_for_status()
            credential = response.json()[0]
            credential_id = credential["id"]
            print(f"  Created credential with ID: {credential_id}")
        except Exception as e:
            print(f"  Error creating credential: {str(e)}")
            if hasattr(e, 'response') and e.response:
                print(f"  Response: {e.response.text}")
            continue
        
        # Step 2: Create a new order
        print("\nStep 2: Creating a new order...")
        order_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "challenge_type": "Test Challenge",
            "account_size": "10K",
            "platform": "MT4",
            "payment_method": "Test Payment",
            "txid": f"test_txid_{random.randint(10000, 99999)}"
        }
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/order/create_order",
                data=order_data
            )
            response.raise_for_status()
            order = response.json()
            order_id = order["id"]
            print(f"  Created order with ID: {order_id}")
        except Exception as e:
            print(f"  Error creating order: {str(e)}")
            if hasattr(e, 'response') and e.response:
                print(f"  Response: {e.response.text}")
            continue
        
        # Step 3: Assign the account to the order with the specified assignment type
        print(f"\nStep 3: Assigning account to order with assignment type: {assignment_type}...")
        profit_target = 1000.0
        drawdown = 5.0
        
        try:
            response = requests.put(
                f"{API_BASE_URL}/account/credentials/{credential_id}/assign/{order_id}?assignment_type={assignment_type}&profit_target={profit_target}&drawdown={drawdown}"
            )
            response.raise_for_status()
            result = response.json()
            print(f"  Account assigned successfully: {result}")
        except Exception as e:
            print(f"  Error assigning account: {str(e)}")
            if hasattr(e, 'response') and e.response:
                print(f"  Response: {e.response.text}")
            continue
        
        # Step 4: Verify the assignment was successful
        print("\nStep 4: Verifying assignment...")
        
        try:
            # Check for complete order record
            response = requests.get(
                f"{API_BASE_URL}/order/complete_orders"
            )
            response.raise_for_status()
            complete_orders = response.json()
            
            # Find the complete order for our order_id
            found_complete = False
            for complete_order in complete_orders:
                if complete_order.get("order_id") == order_id:
                    found_complete = True
                    print(f"  Complete order record found for order ID {order_id}")
                    break
            
            if not found_complete:
                print(f"  Warning: Complete order record for order ID {order_id} not found")
            
            # If stage2 assignment, check for stage2 account
            if assignment_type == "stage2":
                response = requests.get(
                    f"{API_BASE_URL}/order/stage2_accounts"
                )
                response.raise_for_status()
                stage2_accounts = response.json()
                
                found_stage2 = False
                for stage2_account in stage2_accounts:
                    if stage2_account.get("order_id") == f"FDH{order_id}":
                        found_stage2 = True
                        print(f"  Stage2 account found for order ID {order_id}")
                        break
                
                if not found_stage2:
                    print(f"  Error: Stage2 account for order ID {order_id} not found")
            
            # If live assignment, check for live account
            if assignment_type == "live":
                response = requests.get(
                    f"{API_BASE_URL}/order/live_accounts"
                )
                response.raise_for_status()
                live_accounts = response.json()
                
                found_live = False
                for live_account in live_accounts:
                    if live_account.get("order_id") == f"FDH{order_id}":
                        found_live = True
                        print(f"  Live account found for order ID {order_id}")
                        break
                
                if not found_live:
                    print(f"  Error: Live account for order ID {order_id} not found")
            
        except Exception as e:
            print(f"  Error verifying assignment: {str(e)}")
            if hasattr(e, 'response') and e.response:
                print(f"  Response: {e.response.text}")
            continue
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_assign_account_with_type()
