-- Simple database fix script - adds all missing columns without deleting anything
-- Run this script on your PostgreSQL database

-- Fix User table
ALTER TABLE "user" ADD COLUMN IF NOT EXISTS referral_code VARCHAR UNIQUE DEFAULT substr(md5(random()::text), 1, 8);
ALTER TABLE "user" ADD COLUMN IF NOT EXISTS referred_by VARCHAR;
ALTER TABLE "user" ADD COLUMN IF NOT EXISTS total_points FLOAT DEFAULT 0;

-- Fix OrderModel table
ALTER TABLE ordermodel ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix CompleteOrderModel table
ALTER TABLE completeordermodel ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix FailOrder table
ALTER TABLE failorder ADD COLUMN IF NOT EXISTS failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix RejectOrder table
ALTER TABLE rejectorder ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix PassOrder table
ALTER TABLE passorder ADD COLUMN IF NOT EXISTS pass_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix OrderTimeline table
ALTER TABLE ordertimeline ADD COLUMN IF NOT EXISTS event_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix OrderImage table
ALTER TABLE orderimage ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Fix LiveAccount table
ALTER TABLE liveaccount ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE liveaccount ADD COLUMN IF NOT EXISTS profit_share FLOAT DEFAULT 80.0;
ALTER TABLE liveaccount ADD COLUMN IF NOT EXISTS status VARCHAR DEFAULT 'active';

-- Fix Stage2Account table
ALTER TABLE stage2account ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE stage2account ADD COLUMN IF NOT EXISTS profit_target FLOAT;
ALTER TABLE stage2account ADD COLUMN IF NOT EXISTS status VARCHAR DEFAULT 'active';

-- Fix Certificate table
ALTER TABLE certificate ADD COLUMN IF NOT EXISTS issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE certificate ADD COLUMN IF NOT EXISTS profit_target FLOAT;

-- Fix DateTimeModel table
ALTER TABLE datetimemodel ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP;

-- Verify the fix
SELECT 'Database fix completed successfully!' as status; 