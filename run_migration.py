#!/usr/bin/env python3
"""
Simple script to run database migration on Heroku
"""

import os
import sys
from sqlmodel import create_engine, Session, text

# Get database URL from environment (<PERSON><PERSON> sets this automatically)
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    print("❌ DATABASE_URL environment variable not set")
    sys.exit(1)

def run_migration():
    """Run the database migration"""
    
    print("🔧 Running database migration...")
    print(f"Database: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'Unknown'}")
        
    # Create engine
    engine = create_engine(DATABASE_URL)
    
    with Session(engine) as session:
        try:
            # Add referred_by column to user table
            print("\n1. Checking user table...")
            result = session.exec(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'user' AND column_name = 'referred_by'
            """)).first()
            
            if not result:
                print("   Adding 'referred_by' column...")
                session.exec(text("ALTER TABLE \"user\" ADD COLUMN referred_by VARCHAR"))
                session.commit()
                print("   ✅ Added 'referred_by' column")
            else:
                print("   ✅ 'referred_by' column already exists")
            
            # Add completed_at column to completeordermodel table
            print("\n2. Checking completeordermodel table...")
            result = session.exec(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'completeordermodel' AND column_name = 'completed_at'
            """)).first()
            
            if not result:
                print("   Adding 'completed_at' column...")
                session.exec(text("ALTER TABLE completeordermodel ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"))
                session.commit()
                print("   ✅ Added 'completed_at' column")
            else:
                print("   ✅ 'completed_at' column already exists")
            
            print("\n✅ Migration completed successfully!")
        
    except Exception as e:
            print(f"\n❌ Migration failed: {e}")
            session.rollback()
            raise

if __name__ == "__main__":
    run_migration()
