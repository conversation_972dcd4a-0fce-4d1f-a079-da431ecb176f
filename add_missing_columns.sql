-- SQL script to add missing columns to database tables
-- Run this script on your PostgreSQL database

-- Add referred_by column to user table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user' AND column_name = 'referred_by'
    ) THEN
        ALTER TABLE "user" ADD COLUMN referred_by VARCHAR;
        RAISE NOTICE 'Added referred_by column to user table';
    ELSE
        RAISE NOTICE 'referred_by column already exists in user table';
    END IF;
END $$;

-- Add completed_at column to completeordermodel table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'completeordermodel' AND column_name = 'completed_at'
    ) THEN
        ALTER TABLE completeordermodel ADD COLUMN completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added completed_at column to completeordermodel table';
    ELSE
        RAISE NOTICE 'completed_at column already exists in completeordermodel table';
    END IF;
END $$;

-- Verify the columns exist
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name IN ('user', 'completeordermodel')
    AND column_name IN ('referred_by', 'completed_at')
ORDER BY table_name, column_name; 