#!/usr/bin/env python3
"""
Comprehensive script to add all missing columns to database tables
"""

import os
import sys
from sqlmodel import SQLModel, create_engine, Session, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database URL - get from environment
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    print("❌ DATABASE_URL environment variable is not set")
    sys.exit(1)

# Ensure correct PostgreSQL format
DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql+psycopg2://", 1)

def add_missing_columns():
    """Add all missing columns to database tables"""
    
    print("🔧 Comprehensive Database Migration")
    print("=" * 50)
    
    # Create engine
    engine = create_engine(
        DATABASE_URL,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    with Session(engine) as session:
        try:
            # 1. User table columns
            print("\n1. Checking User table...")
            user_columns = [
                ("referred_by", "VARCHAR"),
            ]
            
            for column_name, column_type in user_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'user' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE "user" ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 2. OrderModel table columns
            print("\n2. Checking OrderModel table...")
            ordermodel_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in ordermodel_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'ordermodel' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE ordermodel ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 3. CompleteOrderModel table columns
            print("\n3. Checking CompleteOrderModel table...")
            completeordermodel_columns = [
                ("completed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in completeordermodel_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'completeordermodel' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE completeordermodel ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 4. FailOrder table columns
            print("\n4. Checking FailOrder table...")
            failorder_columns = [
                ("failed_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in failorder_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'failorder' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE failorder ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 5. RejectOrder table columns
            print("\n5. Checking RejectOrder table...")
            rejectorder_columns = [
                ("rejected_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in rejectorder_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'rejectorder' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE rejectorder ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 6. PassOrder table columns
            print("\n6. Checking PassOrder table...")
            passorder_columns = [
                ("pass_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in passorder_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'passorder' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE passorder ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 7. OrderTimeline table columns
            print("\n7. Checking OrderTimeline table...")
            ordertimeline_columns = [
                ("event_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in ordertimeline_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'ordertimeline' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE ordertimeline ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 8. OrderImage table columns
            print("\n8. Checking OrderImage table...")
            orderimage_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ]
            
            for column_name, column_type in orderimage_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'orderimage' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE orderimage ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 9. LiveAccount table columns
            print("\n9. Checking LiveAccount table...")
            liveaccount_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("profit_share", "FLOAT DEFAULT 80.0"),
                ("status", "VARCHAR DEFAULT 'active'"),
            ]
            
            for column_name, column_type in liveaccount_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'liveaccount' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE liveaccount ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 10. Stage2Account table columns
            print("\n10. Checking Stage2Account table...")
            stage2account_columns = [
                ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("profit_target", "FLOAT"),
                ("status", "VARCHAR DEFAULT 'active'"),
            ]
            
            for column_name, column_type in stage2account_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'stage2account' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE stage2account ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 11. Certificate table columns
            print("\n11. Checking Certificate table...")
            certificate_columns = [
                ("issue_date", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("profit_target", "FLOAT"),
            ]
            
            for column_name, column_type in certificate_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'certificate' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE certificate ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            # 12. DateTimeModel table columns
            print("\n12. Checking DateTimeModel table...")
            datetimemodel_columns = [
                ("updated_at", "TIMESTAMP"),
            ]
            
            for column_name, column_type in datetimemodel_columns:
                result = session.exec(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'datetimemodel' AND column_name = '{column_name}'
                """)).first()
                
                if not result:
                    print(f"   Adding '{column_name}' column...")
                    session.exec(text(f'ALTER TABLE datetimemodel ADD COLUMN {column_name} {column_type}'))
                    session.commit()
                    print(f"   ✅ Added '{column_name}' column")
                else:
                    print(f"   ✅ '{column_name}' column already exists")

            print("\n✅ All migrations completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Migration failed: {e}")
            session.rollback()
            raise

def verify_all_columns():
    """Verify that all required columns exist"""
    print("\n🔍 Verifying all columns...")
    
    engine = create_engine(DATABASE_URL, echo=False)
    
    with Session(engine) as session:
        try:
            # List of tables to check
            tables_to_check = [
                "user", "ordermodel", "completeordermodel", "failorder", 
                "rejectorder", "passorder", "ordertimeline", "orderimage",
                "liveaccount", "stage2account", "certificate", "datetimemodel"
            ]
            
            for table_name in tables_to_check:
                print(f"\n   Checking {table_name} table...")
                result = session.exec(text(f"""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}'
                    ORDER BY column_name
                """)).all()
                
                if result:
                    print(f"   ✅ {table_name} table exists with {len(result)} columns:")
                    for row in result:
                        print(f"     - {row[0]} ({row[1]})")
                else:
                    print(f"   ❌ {table_name} table not found")
                    
        except Exception as e:
            print(f"   ❌ Error verifying columns: {e}")
            raise

def main():
    """Main function"""
    try:
        add_missing_columns()
        verify_all_columns()
        print("\n🎉 All database migrations completed successfully!")
        print("   Your application should now work without column errors.")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 